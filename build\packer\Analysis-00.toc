(['C:\\Users\\<USER>\\Desktop\\Packet_en\\packer.py'],
 ['C:\\Users\\<USER>\\Desktop\\Packet_en'],
 [],
 ['C:\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'C:\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('packer', 'C:\\Users\\<USER>\\Desktop\\Packet_en\\packer.py', 'PYSOURCE')],
 [('_pyi_rth_utils',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Python312\\Lib\\zipimport.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('typing', 'C:\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('importlib.resources',
   'C:\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'C:\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'C:\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'C:\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('copy', 'C:\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('struct', 'C:\\Python312\\Lib\\struct.py', 'PYMODULE'),
  ('string', 'C:\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('bisect', 'C:\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('shutil', 'C:\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('argparse', 'C:\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('gettext', 'C:\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'C:\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'C:\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'C:\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'C:\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'C:\\Python312\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'C:\\Python312\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python312\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'C:\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\Python312\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'C:\\Python312\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'C:\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('socket', 'C:\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'C:\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('email', 'C:\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python312\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('csv', 'C:\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('inspect', 'C:\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'C:\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib', 'C:\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('signal', 'C:\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python312\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'C:\\Python312\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python312\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'C:\\Python312\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python312\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python312\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'C:\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python312\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python312\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'C:\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'C:\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python312\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python312\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'C:\\Python312\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python312\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'C:\\Python312\\Lib\\http\\client.py', 'PYMODULE'),
  ('hmac', 'C:\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Python312\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\Python312\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'C:\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Python312\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('subprocess', 'C:\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'C:\\Python312\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('tracemalloc', 'C:\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('utils.util_fn',
   'C:\\Users\\<USER>\\Desktop\\Packet_en\\utils\\util_fn.py',
   'PYMODULE'),
  ('utils',
   'C:\\Users\\<USER>\\Desktop\\Packet_en\\utils\\__init__.py',
   'PYMODULE'),
  ('encrypt', 'C:\\Users\\<USER>\\Desktop\\Packet_en\\encrypt.py', 'PYMODULE'),
  ('json', 'C:\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python312\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Python312\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Python312\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('threading', 'C:\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python312\\Lib\\_threading_local.py', 'PYMODULE'),
  ('scapy.all', 'C:\\Python312\\Lib\\site-packages\\scapy\\all.py', 'PYMODULE'),
  ('scapy',
   'C:\\Python312\\Lib\\site-packages\\scapy\\__init__.py',
   'PYMODULE'),
  ('scapy.ansmachine',
   'C:\\Python312\\Lib\\site-packages\\scapy\\ansmachine.py',
   'PYMODULE'),
  ('scapy.libs.six',
   'C:\\Python312\\Lib\\site-packages\\scapy\\libs\\six.py',
   'PYMODULE'),
  ('scapy.libs',
   'C:\\Python312\\Lib\\site-packages\\scapy\\libs\\__init__.py',
   'PYMODULE'),
  ('__future__', 'C:\\Python312\\Lib\\__future__.py', 'PYMODULE'),
  ('scapy.route6',
   'C:\\Python312\\Lib\\site-packages\\scapy\\route6.py',
   'PYMODULE'),
  ('scapy.pton_ntop',
   'C:\\Python312\\Lib\\site-packages\\scapy\\pton_ntop.py',
   'PYMODULE'),
  ('scapy.utils6',
   'C:\\Python312\\Lib\\site-packages\\scapy\\utils6.py',
   'PYMODULE'),
  ('scapy.scapypipes',
   'C:\\Python312\\Lib\\site-packages\\scapy\\scapypipes.py',
   'PYMODULE'),
  ('scapy.layers.inet',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\inet.py',
   'PYMODULE'),
  ('scapy.layers.inet6',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\inet6.py',
   'PYMODULE'),
  ('scapy.layers',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\__init__.py',
   'PYMODULE'),
  ('scapy.libs.matplot',
   'C:\\Python312\\Lib\\site-packages\\scapy\\libs\\matplot.py',
   'PYMODULE'),
  ('scapy.layers.l2',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\l2.py',
   'PYMODULE'),
  ('scapy.pipetool',
   'C:\\Python312\\Lib\\site-packages\\scapy\\pipetool.py',
   'PYMODULE'),
  ('scapy.asn1.mib',
   'C:\\Python312\\Lib\\site-packages\\scapy\\asn1\\mib.py',
   'PYMODULE'),
  ('scapy.asn1',
   'C:\\Python312\\Lib\\site-packages\\scapy\\asn1\\__init__.py',
   'PYMODULE'),
  ('glob', 'C:\\Python312\\Lib\\glob.py', 'PYMODULE'),
  ('scapy.asn1.ber',
   'C:\\Python312\\Lib\\site-packages\\scapy\\asn1\\ber.py',
   'PYMODULE'),
  ('scapy.asn1.asn1',
   'C:\\Python312\\Lib\\site-packages\\scapy\\asn1\\asn1.py',
   'PYMODULE'),
  ('scapy.layers.all',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\all.py',
   'PYMODULE'),
  ('scapy.layers.zigbee',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\zigbee.py',
   'PYMODULE'),
  ('scapy.layers.x509',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\x509.py',
   'PYMODULE'),
  ('scapy.layers.vxlan',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\vxlan.py',
   'PYMODULE'),
  ('scapy.layers.vrrp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\vrrp.py',
   'PYMODULE'),
  ('scapy.layers.usb',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\usb.py',
   'PYMODULE'),
  ('scapy.layers.tuntap',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tuntap.py',
   'PYMODULE'),
  ('scapy.layers.tls.tools',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\tools.py',
   'PYMODULE'),
  ('scapy.layers.tls.session',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\session.py',
   'PYMODULE'),
  ('scapy.layers.tls.record_tls13',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\record_tls13.py',
   'PYMODULE'),
  ('scapy.layers.tls.record_sslv2',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\record_sslv2.py',
   'PYMODULE'),
  ('scapy.layers.tls.record',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\record.py',
   'PYMODULE'),
  ('scapy.layers.tls.keyexchange_tls13',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\keyexchange_tls13.py',
   'PYMODULE'),
  ('scapy.layers.tls.keyexchange',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\keyexchange.py',
   'PYMODULE'),
  ('scapy.layers.tls.handshake_sslv2',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\handshake_sslv2.py',
   'PYMODULE'),
  ('scapy.layers.tls.handshake',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\handshake.py',
   'PYMODULE'),
  ('scapy.layers.tls.extensions',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\extensions.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.suites',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\suites.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.prf',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\prf.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.pkcs1',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\pkcs1.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.md4',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\md4.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.kx_algs',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\kx_algs.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.hkdf',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\hkdf.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.hash',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\hash.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.h_mac',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\h_mac.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.groups',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\groups.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.compression',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\compression.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.common',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\common.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.ciphers',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\ciphers.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.cipher_stream',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\cipher_stream.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.cipher_block',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\cipher_block.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.cipher_aead',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\cipher_aead.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto.all',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\all.py',
   'PYMODULE'),
  ('scapy.layers.tls.crypto',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\crypto\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.tls.cert',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\cert.py',
   'PYMODULE'),
  ('scapy.layers.tls.basefields',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\basefields.py',
   'PYMODULE'),
  ('scapy.layers.tls.automaton_srv',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\automaton_srv.py',
   'PYMODULE'),
  ('scapy.layers.tls.automaton_cli',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\automaton_cli.py',
   'PYMODULE'),
  ('scapy.layers.tls.automaton',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\automaton.py',
   'PYMODULE'),
  ('scapy.layers.tls.all',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\all.py',
   'PYMODULE'),
  ('scapy.layers.tls',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tls\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.tftp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\tftp.py',
   'PYMODULE'),
  ('scapy.layers.snmp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\snmp.py',
   'PYMODULE'),
  ('scapy.layers.smbserver',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\smbserver.py',
   'PYMODULE'),
  ('scapy.layers.smbclient',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\smbclient.py',
   'PYMODULE'),
  ('scapy.layers.smb2',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\smb2.py',
   'PYMODULE'),
  ('scapy.layers.smb',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\smb.py',
   'PYMODULE'),
  ('scapy.layers.skinny',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\skinny.py',
   'PYMODULE'),
  ('scapy.layers.sixlowpan',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\sixlowpan.py',
   'PYMODULE'),
  ('scapy.layers.sctp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\sctp.py',
   'PYMODULE'),
  ('scapy.layers.rtp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\rtp.py',
   'PYMODULE'),
  ('scapy.layers.rip',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\rip.py',
   'PYMODULE'),
  ('scapy.layers.radius',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\radius.py',
   'PYMODULE'),
  ('scapy.layers.pptp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\pptp.py',
   'PYMODULE'),
  ('scapy.layers.ppp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\ppp.py',
   'PYMODULE'),
  ('scapy.layers.ppi',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\ppi.py',
   'PYMODULE'),
  ('scapy.layers.pflog',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\pflog.py',
   'PYMODULE'),
  ('scapy.layers.ntp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\ntp.py',
   'PYMODULE'),
  ('scapy.layers.ntlm',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\ntlm.py',
   'PYMODULE'),
  ('scapy.layers.netflow',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\netflow.py',
   'PYMODULE'),
  ('scapy.layers.netbios',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\netbios.py',
   'PYMODULE'),
  ('scapy.layers.mspac',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\mspac.py',
   'PYMODULE'),
  ('scapy.layers.mobileip',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\mobileip.py',
   'PYMODULE'),
  ('scapy.layers.mgcp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\mgcp.py',
   'PYMODULE'),
  ('scapy.layers.lltd',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\lltd.py',
   'PYMODULE'),
  ('scapy.layers.llmnr',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\llmnr.py',
   'PYMODULE'),
  ('scapy.layers.ldap',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\ldap.py',
   'PYMODULE'),
  ('scapy.layers.l2tp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\l2tp.py',
   'PYMODULE'),
  ('scapy.layers.kerberos',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\kerberos.py',
   'PYMODULE'),
  ('scapy.libs.rfc3961',
   'C:\\Python312\\Lib\\site-packages\\scapy\\libs\\rfc3961.py',
   'PYMODULE'),
  ('scapy.layers.isakmp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\isakmp.py',
   'PYMODULE'),
  ('scapy.layers.ir',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\ir.py',
   'PYMODULE'),
  ('scapy.layers.ipsec',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\ipsec.py',
   'PYMODULE'),
  ('scapy.layers.http',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\http.py',
   'PYMODULE'),
  ('scapy.contrib.http2',
   'C:\\Python312\\Lib\\site-packages\\scapy\\contrib\\http2.py',
   'PYMODULE'),
  ('scapy.contrib',
   'C:\\Python312\\Lib\\site-packages\\scapy\\contrib\\__init__.py',
   'PYMODULE'),
  ('scapy.layers.hsrp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\hsrp.py',
   'PYMODULE'),
  ('scapy.layers.gssapi',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\gssapi.py',
   'PYMODULE'),
  ('uuid', 'C:\\Python312\\Lib\\uuid.py', 'PYMODULE'),
  ('platform', 'C:\\Python312\\Lib\\platform.py', 'PYMODULE'),
  ('scapy.layers.gprs',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\gprs.py',
   'PYMODULE'),
  ('scapy.layers.eap',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\eap.py',
   'PYMODULE'),
  ('scapy.layers.dot15d4',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\dot15d4.py',
   'PYMODULE'),
  ('scapy.layers.dot11',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\dot11.py',
   'PYMODULE'),
  ('scapy.layers.dns',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\dns.py',
   'PYMODULE'),
  ('scapy.layers.dhcp6',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\dhcp6.py',
   'PYMODULE'),
  ('scapy.layers.dhcp',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\dhcp.py',
   'PYMODULE'),
  ('scapy.layers.dcerpc',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\dcerpc.py',
   'PYMODULE'),
  ('scapy.contrib.rtps.common_types',
   'C:\\Python312\\Lib\\site-packages\\scapy\\contrib\\rtps\\common_types.py',
   'PYMODULE'),
  ('scapy.contrib.rtps',
   'C:\\Python312\\Lib\\site-packages\\scapy\\contrib\\rtps\\__init__.py',
   'PYMODULE'),
  ('scapy.contrib.rtps.pid_types',
   'C:\\Python312\\Lib\\site-packages\\scapy\\contrib\\rtps\\pid_types.py',
   'PYMODULE'),
  ('scapy.contrib.rtps.rtps',
   'C:\\Python312\\Lib\\site-packages\\scapy\\contrib\\rtps\\rtps.py',
   'PYMODULE'),
  ('scapy.layers.clns',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\clns.py',
   'PYMODULE'),
  ('scapy.layers.can',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\can.py',
   'PYMODULE'),
  ('scapy.layers.bluetooth4LE',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\bluetooth4LE.py',
   'PYMODULE'),
  ('scapy.contrib.ethercat',
   'C:\\Python312\\Lib\\site-packages\\scapy\\contrib\\ethercat.py',
   'PYMODULE'),
  ('scapy.layers.bluetooth',
   'C:\\Python312\\Lib\\site-packages\\scapy\\layers\\bluetooth.py',
   'PYMODULE'),
  ('scapy.compat',
   'C:\\Python312\\Lib\\site-packages\\scapy\\compat.py',
   'PYMODULE'),
  ('html', 'C:\\Python312\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python312\\Lib\\html\\entities.py', 'PYMODULE'),
  ('cgi', 'C:\\Python312\\Lib\\cgi.py', 'PYMODULE'),
  ('scapy.consts',
   'C:\\Python312\\Lib\\site-packages\\scapy\\consts.py',
   'PYMODULE'),
  ('scapy.main',
   'C:\\Python312\\Lib\\site-packages\\scapy\\main.py',
   'PYMODULE'),
  ('code', 'C:\\Python312\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python312\\Lib\\codeop.py', 'PYMODULE'),
  ('scapy.autorun',
   'C:\\Python312\\Lib\\site-packages\\scapy\\autorun.py',
   'PYMODULE'),
  ('scapy.automaton',
   'C:\\Python312\\Lib\\site-packages\\scapy\\automaton.py',
   'PYMODULE'),
  ('scapy.as_resolvers',
   'C:\\Python312\\Lib\\site-packages\\scapy\\as_resolvers.py',
   'PYMODULE'),
  ('scapy.volatile',
   'C:\\Python312\\Lib\\site-packages\\scapy\\volatile.py',
   'PYMODULE'),
  ('scapy.supersocket',
   'C:\\Python312\\Lib\\site-packages\\scapy\\supersocket.py',
   'PYMODULE'),
  ('scapy.arch.linux',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\linux.py',
   'PYMODULE'),
  ('scapy.libs.structures',
   'C:\\Python312\\Lib\\site-packages\\scapy\\libs\\structures.py',
   'PYMODULE'),
  ('scapy.arch.unix',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\unix.py',
   'PYMODULE'),
  ('scapy.arch.common',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\common.py',
   'PYMODULE'),
  ('scapy.libs.winpcapy',
   'C:\\Python312\\Lib\\site-packages\\scapy\\libs\\winpcapy.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\Python312\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python312\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python312\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python312\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python312\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python312\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('scapy.sessions',
   'C:\\Python312\\Lib\\site-packages\\scapy\\sessions.py',
   'PYMODULE'),
  ('scapy.sendrecv',
   'C:\\Python312\\Lib\\site-packages\\scapy\\sendrecv.py',
   'PYMODULE'),
  ('scapy.route',
   'C:\\Python312\\Lib\\site-packages\\scapy\\route.py',
   'PYMODULE'),
  ('scapy.utils',
   'C:\\Python312\\Lib\\site-packages\\scapy\\utils.py',
   'PYMODULE'),
  ('difflib', 'C:\\Python312\\Lib\\difflib.py', 'PYMODULE'),
  ('scapy.asn1packet',
   'C:\\Python312\\Lib\\site-packages\\scapy\\asn1packet.py',
   'PYMODULE'),
  ('scapy.asn1fields',
   'C:\\Python312\\Lib\\site-packages\\scapy\\asn1fields.py',
   'PYMODULE'),
  ('scapy.packet',
   'C:\\Python312\\Lib\\site-packages\\scapy\\packet.py',
   'PYMODULE'),
  ('scapy.libs.test_pyx',
   'C:\\Python312\\Lib\\site-packages\\scapy\\libs\\test_pyx.py',
   'PYMODULE'),
  ('scapy.fields',
   'C:\\Python312\\Lib\\site-packages\\scapy\\fields.py',
   'PYMODULE'),
  ('scapy.plist',
   'C:\\Python312\\Lib\\site-packages\\scapy\\plist.py',
   'PYMODULE'),
  ('scapy.interfaces',
   'C:\\Python312\\Lib\\site-packages\\scapy\\interfaces.py',
   'PYMODULE'),
  ('scapy.arch.windows',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\windows\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.libpcap',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\libpcap.py',
   'PYMODULE'),
  ('scapy.arch.windows.structures',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\windows\\structures.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python312\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('scapy.arch',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.windows.native',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\windows\\native.py',
   'PYMODULE'),
  ('scapy.arch.solaris',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\solaris.py',
   'PYMODULE'),
  ('scapy.arch.bpf.supersocket',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\bpf\\supersocket.py',
   'PYMODULE'),
  ('scapy.arch.bpf',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\bpf\\__init__.py',
   'PYMODULE'),
  ('scapy.arch.bpf.consts',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\bpf\\consts.py',
   'PYMODULE'),
  ('scapy.arch.bpf.core',
   'C:\\Python312\\Lib\\site-packages\\scapy\\arch\\bpf\\core.py',
   'PYMODULE'),
  ('scapy.themes',
   'C:\\Python312\\Lib\\site-packages\\scapy\\themes.py',
   'PYMODULE'),
  ('scapy.error',
   'C:\\Python312\\Lib\\site-packages\\scapy\\error.py',
   'PYMODULE'),
  ('scapy.data',
   'C:\\Python312\\Lib\\site-packages\\scapy\\data.py',
   'PYMODULE'),
  ('scapy.libs.ethertypes',
   'C:\\Python312\\Lib\\site-packages\\scapy\\libs\\ethertypes.py',
   'PYMODULE'),
  ('scapy.dadict',
   'C:\\Python312\\Lib\\site-packages\\scapy\\dadict.py',
   'PYMODULE'),
  ('scapy.config',
   'C:\\Python312\\Lib\\site-packages\\scapy\\config.py',
   'PYMODULE'),
  ('scapy.base_classes',
   'C:\\Python312\\Lib\\site-packages\\scapy\\base_classes.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'C:\\Python312\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Python312\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\Python312\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinter', 'C:\\Python312\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('sqlite3', 'C:\\Python312\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'C:\\Python312\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.__main__', 'C:\\Python312\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'C:\\Python312\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('datetime', 'C:\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python312\\Lib\\_pydatetime.py', 'PYMODULE')],
 [('python312.dll', 'C:\\Python312\\python312.dll', 'BINARY'),
  ('_decimal.pyd', 'C:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python312\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python312\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'C:\\Python312\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\Python312\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'C:\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-3.dll', 'C:\\Python312\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python312\\DLLs\\libffi-8.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python312\\VCRUNTIME140_1.dll', 'BINARY'),
  ('tk86t.dll', 'C:\\Python312\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'C:\\Python312\\DLLs\\tcl86t.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Python312\\DLLs\\sqlite3.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('zlib1.dll', 'C:\\Python312\\DLLs\\zlib1.dll', 'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\Packet_en\\build\\packer\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\msgs\\zh.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\zh.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tk\\dialog.tcl', 'C:\\Python312\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\GMT0', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\GMT0', 'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\msgs\\sr.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\sr.msg', 'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'C:\\Python312\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tk\\iconlist.tcl', 'C:\\Python312\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\msgs\\sw.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\sw.msg', 'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Python312\\tcl\\tcl8.6\\..\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tk\\msgs\\nl.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'C:\\Python312\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\msgs\\fi.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\fi.msg', 'DATA'),
  ('tcl\\encoding\\cns11643.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tk\\msgs\\es.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('tcl\\msgs\\sk.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\sk.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tk\\msgs\\cs.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('tcl\\tzdata\\Singapore',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\msgs\\pl.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\pl.msg', 'DATA'),
  ('tcl\\msgs\\es.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\es.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tk\\msgs\\eo.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\Python312\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'C:\\Python312\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\UTC', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('tcl\\tzdata\\Universal',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\EET', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\msgs\\ga.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\ga.msg', 'DATA'),
  ('tcl\\tzdata\\GMT', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\MST', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tk\\console.tcl', 'C:\\Python312\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tk\\megawidget.tcl', 'C:\\Python312\\tcl\\tk8.6\\megawidget.tcl', 'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\msgs\\el.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\el.msg', 'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tk\\mkpsenc.tcl', 'C:\\Python312\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\msgs\\hu.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\hu.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tk\\msgs\\el.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tk\\msgs\\it.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.5.tm',
   'C:\\Python312\\tcl\\tcl8.6\\..\\tcl8\\8.5\\tcltest-2.5.5.tm',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl\\msgs\\ca.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\ca.msg', 'DATA'),
  ('tk\\msgs\\sv.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\msgs\\bn.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\bn.msg', 'DATA'),
  ('tcl\\tzdata\\UCT', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tk\\entry.tcl', 'C:\\Python312\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('tk\\panedwindow.tcl', 'C:\\Python312\\tcl\\tk8.6\\panedwindow.tcl', 'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\msgs\\hr.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\hr.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Iran', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Iran', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\msgs\\be.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\be.msg', 'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\Navajo', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Navajo', 'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\msgs\\ro.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\ro.msg', 'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\CET', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tk\\images\\README', 'C:\\Python312\\tcl\\tk8.6\\images\\README', 'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tclIndex', 'C:\\Python312\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\ROK', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\encoding\\iso8859-11.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\Egypt', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Egypt', 'DATA'),
  ('tk\\ttk\\utils.tcl', 'C:\\Python312\\tcl\\tk8.6\\ttk\\utils.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\msgs\\sq.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\sq.msg', 'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Nuuk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('tcl\\msgs\\hi.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\hi.msg', 'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\MET', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\msgs\\fo.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\fo.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\msgs\\sh.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\sh.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tk\\optMenu.tcl', 'C:\\Python312\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\msgs\\de.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\de.msg', 'DATA'),
  ('tk\\msgs\\pl.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\Cuba', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Cuba', 'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\msgs\\mr.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\mr.msg', 'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\msgs\\he.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\he.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tk\\msgs\\de.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('tk\\clrpick.tcl', 'C:\\Python312\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\Python312\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\Japan', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Japan', 'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\msgs\\ta.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\ta.msg', 'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\Python312\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\tm.tcl', 'C:\\Python312\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\msgs\\lt.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\lt.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tk\\msgs\\ru.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\msgs\\te.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\te.msg', 'DATA'),
  ('tcl\\msgs\\sl.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\sl.msg', 'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\msgs\\th.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\th.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tk\\msgs\\da.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\auto.tcl', 'C:\\Python312\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('tcl\\tzdata\\Iceland',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\msgs\\kok.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\kok.msg', 'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\msgs\\is.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\is.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tk\\comdlg.tcl', 'C:\\Python312\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\tzdata\\GB', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\tzdata\\GMT+0', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\GMT+0', 'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\tzdata\\PRC', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qostanay',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tk\\icons.tcl', 'C:\\Python312\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\word.tcl', 'C:\\Python312\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tk\\focus.tcl', 'C:\\Python312\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tk\\ttk\\ttk.tcl', 'C:\\Python312\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\msgs\\et.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\et.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\history.tcl', 'C:\\Python312\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tk\\unsupported.tcl', 'C:\\Python312\\tcl\\tk8.6\\unsupported.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\msgs\\da.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\da.msg', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tk\\spinbox.tcl', 'C:\\Python312\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('tk\\safetk.tcl', 'C:\\Python312\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tk\\listbox.tcl', 'C:\\Python312\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\msgs\\nn.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\nn.msg', 'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\msgs\\ar.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\ar.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\Turkey', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Turkey', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\msgs\\id.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\id.msg', 'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\Python312\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tk\\scale.tcl', 'C:\\Python312\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tk\\text.tcl', 'C:\\Python312\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tk\\fontchooser.tcl', 'C:\\Python312\\tcl\\tk8.6\\fontchooser.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tk\\msgs\\fr.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\msgs\\fa.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\fa.msg', 'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\Python312\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\Python312\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tk\\ttk\\scale.tcl', 'C:\\Python312\\tcl\\tk8.6\\ttk\\scale.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\package.tcl', 'C:\\Python312\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\msgs\\af.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\af.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tk\\tk.tcl', 'C:\\Python312\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:\\Python312\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\Python312\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\msgs\\lv.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\lv.msg', 'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\msgs\\fr.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\fr.msg', 'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\Python312\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\Python312\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tk\\msgs\\en.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\HST', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\NZ', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('tcl\\tzdata\\Israel', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Israel', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kanton',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\msgs\\kl.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\kl.msg', 'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tk\\bgerror.tcl', 'C:\\Python312\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\msgs\\gl.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\gl.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Kyiv',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tk\\msgs\\hu.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:\\Python312\\tcl\\tcl8.6\\..\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\msgs\\ja.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\ja.msg', 'DATA'),
  ('tk\\pkgIndex.tcl', 'C:\\Python312\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tk\\choosedir.tcl', 'C:\\Python312\\tcl\\tk8.6\\choosedir.tcl', 'DATA'),
  ('tcl\\clock.tcl', 'C:\\Python312\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('tk\\msgbox.tcl', 'C:\\Python312\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\msgs\\ko.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\ko.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\msgs\\bg.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\bg.msg', 'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Python312\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\safe.tcl', 'C:\\Python312\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tk\\scrlbar.tcl', 'C:\\Python312\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tk\\tclIndex', 'C:\\Python312\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\GMT-0', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\GMT-0', 'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\msgs\\gv.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\gv.msg', 'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\Python312\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tk\\msgs\\fi.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\fi.msg', 'DATA'),
  ('tcl\\msgs\\mk.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\mk.msg', 'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'C:\\Python312\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\tzdata\\WET', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Poland', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Poland', 'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\msgs\\kw.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\kw.msg', 'DATA'),
  ('tk\\msgs\\en_gb.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\en_gb.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\tzdata\\ROC', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\tzdata\\W-SU', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\W-SU', 'DATA'),
  ('tcl\\msgs\\sv.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\sv.msg', 'DATA'),
  ('tcl\\encoding\\big5.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\Python312\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tk\\palette.tcl', 'C:\\Python312\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tk\\menu.tcl', 'C:\\Python312\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\msgs\\cs.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\cs.msg', 'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tk\\button.tcl', 'C:\\Python312\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tcl\\parray.tcl', 'C:\\Python312\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('tk\\tearoff.tcl', 'C:\\Python312\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('tcl\\tzdata\\EST', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\msgs\\uk.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\uk.msg', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\msgs\\mt.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\mt.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\msgs\\ru.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\ru.msg', 'DATA'),
  ('tk\\msgs\\pt.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Libya', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Libya', 'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\Python312\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tk\\msgs\\zh_cn.msg', 'C:\\Python312\\tcl\\tk8.6\\msgs\\zh_cn.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tk\\ttk\\entry.tcl', 'C:\\Python312\\tcl\\tk8.6\\ttk\\entry.tcl', 'DATA'),
  ('tcl\\init.tcl', 'C:\\Python312\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tzdata\\Eire', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Eire', 'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tk\\license.terms', 'C:\\Python312\\tcl\\tk8.6\\license.terms', 'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tk\\xmfbox.tcl', 'C:\\Python312\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\msgs\\eo.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\eo.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\msgs\\vi.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\vi.msg', 'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tk\\tkfbox.tcl', 'C:\\Python312\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\msgs\\ms.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\ms.msg', 'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tk\\ttk\\fonts.tcl', 'C:\\Python312\\tcl\\tk8.6\\ttk\\fonts.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\Python312\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl\\msgs\\eu.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\eu.msg', 'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Zulu', 'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Zulu', 'DATA'),
  ('tcl\\msgs\\it.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\it.msg', 'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\msgs\\nl.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\nl.msg', 'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'C:\\Python312\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\msgs\\nb.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\nb.msg', 'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tk\\obsolete.tcl', 'C:\\Python312\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tk\\ttk\\button.tcl', 'C:\\Python312\\tcl\\tk8.6\\ttk\\button.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\msgs\\pt.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\pt.msg', 'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'C:\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\msgs\\tr.msg', 'C:\\Python312\\tcl\\tcl8.6\\msgs\\tr.msg', 'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'C:\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA')])
