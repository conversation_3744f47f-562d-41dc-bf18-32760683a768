package parser

import (
	"bytes"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"strings"
	"unicode"

	"1l4e/neo_packer/internal/models"
)

// Constants for packet parsing
const (
	PacketStartMarker = "\xAA\x55"
	PacketEndMarker   = "\x55\xAA"
)

// Parser handles packet parsing functionality
type Parser struct {
	buffer []byte
}

// NewParser creates a new packet parser
func NewParser() *Parser {
	return &Parser{
		buffer: make([]byte, 0),
	}
}

// ProcessPacket processes a raw packet and extracts game protocol information
func (p *Parser) ProcessPacket(packet models.Packet) ([]models.Packet, error) {
	var processedPackets []models.Packet

	// Add data to buffer
	p.buffer = append(p.buffer, packet.RawData...)

	// Process complete packets in buffer
	for {
		// Find packet boundaries
		startIdx := bytes.Index(p.buffer, []byte(PacketStartMarker))
		if startIdx == -1 {
			break
		}

		endIdx := bytes.Index(p.buffer[startIdx:], []byte(PacketEndMarker))
		if endIdx == -1 {
			break
		}
		endIdx += startIdx + len(PacketEndMarker)

		// Extract packet
		if endIdx > len(p.buffer) {
			break
		}

		subPacket := p.buffer[startIdx:endIdx]
		p.buffer = p.buffer[endIdx:]

		// Decrypt if needed (placeholder for actual decryption)
		// if isEncrypted && len(subPacket) >= 16 {
		//     subPacket = decrypt(subPacket)
		// }

		// Parse packet type and world ID
		requestType, worldID := p.parsePacketType(subPacket)

		// Create processed packet
		processedPacket := packet
		processedPacket.RawData = subPacket
		processedPacket.RequestType = requestType
		processedPacket.WorldID = worldID

		processedPackets = append(processedPackets, processedPacket)
	}

	// Clean up buffer if it's getting too large
	if len(p.buffer) > 1024*1024 { // 1MB limit
		p.buffer = make([]byte, 0)
	}

	return processedPackets, nil
}

// parsePacketType extracts the request type and world ID from a packet
// This is a placeholder implementation - actual parsing depends on the game protocol
func (p *Parser) parsePacketType(data []byte) (int, int) {
	// Placeholder implementation
	// In a real implementation, this would parse according to the game's protocol
	if len(data) < 8 {
		return 0, 0
	}

	// Example: Extract request type from bytes 4-5 (little endian)
	requestType := int(binary.LittleEndian.Uint16(data[4:6]))

	// Example: Extract world ID from bytes 6-7 (little endian)
	worldID := int(binary.LittleEndian.Uint16(data[6:8]))

	return requestType, worldID
}

// FormatHexData formats binary data for display
func FormatHexData(data []byte) []models.HexData {
	var result []models.HexData

	// Process in chunks of 16 bytes
	for i := 0; i < len(data); i += 16 {
		end := i + 16
		if end > len(data) {
			end = len(data)
		}

		chunk := data[i:end]

		// Format address
		address := fmt.Sprintf("%08X", i)

		// Format hex string
		hexStr := hex.EncodeToString(chunk)
		hexStr = strings.ToUpper(insertSpaces(hexStr))

		// Format ASCII representation
		byteStr := ""
		for _, b := range chunk {
			if unicode.IsPrint(rune(b)) && b < 128 {
				byteStr += string(b)
			} else {
				byteStr += "."
			}
		}

		result = append(result, models.HexData{
			Address: address,
			HexStr:  hexStr,
			ByteStr: byteStr,
		})
	}

	return result
}

// insertSpaces inserts a space every two characters
func insertSpaces(s string) string {
	var result strings.Builder
	for i, char := range s {
		if i > 0 && i%2 == 0 {
			result.WriteRune(' ')
		}
		result.WriteRune(char)
	}
	return result.String()
}

// InterpretData provides different interpretations of binary data
func InterpretData(data []byte) models.DataInterpretation {
	result := models.DataInterpretation{
		Hex: hex.EncodeToString(data),
	}

	// Handle different data sizes
	if len(data) >= 1 {
		result.Int8 = int8(data[0])
		result.UInt8 = data[0]
	}

	if len(data) >= 2 {
		result.Int16 = int16(binary.LittleEndian.Uint16(data[:2]))
		result.UInt16 = binary.LittleEndian.Uint16(data[:2])
	}

	if len(data) >= 4 {
		result.Int32 = int32(binary.LittleEndian.Uint32(data[:4]))
		result.UInt32 = binary.LittleEndian.Uint32(data[:4])

		// Float32
		bits := binary.LittleEndian.Uint32(data[:4])
		result.Float32 = float32(bits)
	}

	if len(data) >= 8 {
		result.Int64 = int64(binary.LittleEndian.Uint64(data[:8]))
		result.UInt64 = binary.LittleEndian.Uint64(data[:8])

		// Float64
		bits := binary.LittleEndian.Uint64(data[:8])
		result.Float64 = float64(bits)
	}

	// String interpretations
	result.ANSIChar = string(data)

	// UTF-16 (if enough data)
	if len(data)%2 == 0 && len(data) > 0 {
		runes := make([]rune, len(data)/2)
		for i := 0; i < len(data); i += 2 {
			runes[i/2] = rune(binary.LittleEndian.Uint16(data[i : i+2]))
		}
		result.WideChar = string(runes)
	}

	return result
}
