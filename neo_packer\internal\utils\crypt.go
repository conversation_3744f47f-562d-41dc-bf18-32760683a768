package utils

import (
	"bytes"
	"encoding/binary"
	"errors"
	"fmt"
)

// Biến toàn c<PERSON>, tư<PERSON>ng đư<PERSON> với private static int key16 = 5761 trong C#
var key16 int16 = 5761

// PacketCrypto quản lý việc mã hóa và giải mã packet
type PacketCrypto struct {
	HeaderKey int16
}

// NewPacketCrypto tạo một đối tượng PacketCrypto mới
func NewPacketCrypto() *PacketCrypto {
	return &PacketCrypto{
		HeaderKey: key16,
	}
}

// ComputeKeySegment tính toán phân đoạn khóa từ giá trị đầu vào
func ComputeKeySegment(value uint32) uint32 {
	eax := value
	ecx := eax ^ 0x3D0000
	ecx = (ecx >> 16) ^ eax
	ecx = (ecx + ecx*8) & 0xFFFFFFFF
	eax = ecx
	eax = (eax >> 4) ^ ecx
	eax = (eax * 0x73C2EB2D) & 0xFFFFFFFF
	ebx := eax
	ebx = (ebx >> 15) ^ eax
	return ebx
}

// Encrypt mã hóa dữ liệu với kích thước xác định
func (pc *PacketCrypto) Encrypt(data []byte, key uint32) ([]byte, error) {
	if len(data) < 1 {
		return data, nil
	}

	result := make([]byte, len(data))
	copy(result, data)

	keySegment := ComputeKeySegment(key)

	// Mã hóa dữ liệu theo khối 4 byte
	if len(result) >= 4 {
		fullBlocks := len(result) / 4
		for i := 0; i < fullBlocks; i++ {
			offset := i * 4
			segment := binary.LittleEndian.Uint32(result[offset : offset+4])
			encryptedSegment := segment ^ keySegment
			binary.LittleEndian.PutUint32(result[offset:], encryptedSegment)
		}
	}

	// Xử lý các byte còn lại
	remainder := len(result) % 4
	if remainder == 1 {
		result[len(result)-1] ^= byte(keySegment & 0xFF)
	} else if remainder == 2 {
		lastTwoBytes := binary.LittleEndian.Uint16(result[len(result)-2:])
		encryptedSegment := lastTwoBytes ^ uint16(keySegment&0xFFFF)
		binary.LittleEndian.PutUint16(result[len(result)-2:], encryptedSegment)
	} else if remainder == 3 {
		lastThreeBytes := make([]byte, 4)
		copy(lastThreeBytes, result[len(result)-3:])
		segment := binary.LittleEndian.Uint32(lastThreeBytes)
		keySegment24 := keySegment & 0xFFFFFF
		encryptedSegment := segment ^ keySegment24
		binary.LittleEndian.PutUint32(lastThreeBytes, encryptedSegment)
		copy(result[len(result)-3:], lastThreeBytes[:3])
	}

	return result, nil
}

// Decrypt giải mã dữ liệu với kích thước xác định
func (pc *PacketCrypto) Decrypt(data []byte, key uint32) ([]byte, error) {
	if len(data) < 1 {
		return data, nil
	}

	result := make([]byte, len(data))
	copy(result, data)

	keySegment := ComputeKeySegment(key)

	// Giải mã dữ liệu theo khối 4 byte
	if len(result) >= 4 {
		fullBlocks := len(result) / 4
		for i := 0; i < fullBlocks; i++ {
			offset := i * 4
			segment := binary.LittleEndian.Uint32(result[offset : offset+4])
			decryptedSegment := segment ^ keySegment
			binary.LittleEndian.PutUint32(result[offset:], decryptedSegment)
		}
	}

	// Xử lý các byte còn lại
	remainder := len(result) % 4
	if remainder == 1 {
		result[len(result)-1] ^= byte(keySegment & 0xFF)
	} else if remainder == 2 {
		lastTwoBytes := binary.LittleEndian.Uint16(result[len(result)-2:])
		decryptedSegment := lastTwoBytes ^ uint16(keySegment&0xFFFF)
		binary.LittleEndian.PutUint16(result[len(result)-2:], decryptedSegment)
	} else if remainder == 3 {
		lastThreeBytes := make([]byte, 4)
		copy(lastThreeBytes, result[len(result)-3:])
		segment := binary.LittleEndian.Uint32(lastThreeBytes)
		keySegment24 := keySegment & 0xFFFFFF
		decryptedSegment := segment ^ keySegment24
		binary.LittleEndian.PutUint32(lastThreeBytes, decryptedSegment)
		copy(result[len(result)-3:], lastThreeBytes[:3])
	}

	return result, nil
}

// EncryptPacket mã hóa một packet hoàn chỉnh
func (pc *PacketCrypto) EncryptPacket(packet []byte) ([]byte, error) {
	if len(packet) < 6 || packet[0] != 0xaa || packet[1] != 0x55 ||
		packet[len(packet)-2] != 0x55 || packet[len(packet)-1] != 0xaa {
		return nil, errors.New("EncryptPacket Error: invalid packet structure")
	}

	packetData := make([]byte, len(packet)-6)
	copy(packetData, packet[4:len(packet)-2])

	encryptedData, err := pc.Encrypt(packetData, uint32(pc.HeaderKey))
	if err != nil {
		return nil, fmt.Errorf("EncryptPacket Error: %v", err)
	}

	encryptedPacket := make([]byte, len(encryptedData)+8)
	copy(encryptedPacket[:2], packet[:2])
	binary.LittleEndian.PutUint16(encryptedPacket[2:4], uint16(len(encryptedData)+2))
	binary.LittleEndian.PutUint16(encryptedPacket[4:6], uint16(pc.HeaderKey))
	copy(encryptedPacket[6:6+len(encryptedData)], encryptedData)
	copy(encryptedPacket[len(encryptedPacket)-2:], packet[len(packet)-2:])

	return encryptedPacket, nil
}

// DecryptPacket giải mã một packet hoàn chỉnh
func (pc *PacketCrypto) DecryptPacket(packet []byte) ([]byte, error) {
	if len(packet) < 8 {
		return nil, fmt.Errorf("DecryptPacket Error: packet quá ngắn, độ dài: %d bytes", len(packet))
	}

	// Log chi tiết cấu trúc header và footer
	if packet[0] != 0xaa || packet[1] != 0x55 {
		return nil, fmt.Errorf("DecryptPacket Error: packet header không hợp lệ: %02X-%02X, cần: AA-55",
			packet[0], packet[1])
	}

	if packet[len(packet)-2] != 0x55 || packet[len(packet)-1] != 0xaa {
		return nil, fmt.Errorf("DecryptPacket Error: packet footer không hợp lệ: %02X-%02X, cần: 55-AA",
			packet[len(packet)-2], packet[len(packet)-1])
	}

	headerKey := int16(binary.LittleEndian.Uint16(packet[4:6]))
	pc.HeaderKey = headerKey

	encryptedData := make([]byte, len(packet)-8)
	copy(encryptedData, packet[6:len(packet)-2])

	decryptedData, err := pc.Decrypt(encryptedData, uint32(headerKey))
	if err != nil {
		return nil, fmt.Errorf("DecryptPacket Error: %v", err)
	}

	packetNew := make([]byte, len(decryptedData)+6)
	copy(packetNew[:2], packet[:2])
	binary.LittleEndian.PutUint16(packetNew[2:4], uint16(len(decryptedData)))
	copy(packetNew[4:4+len(decryptedData)], decryptedData)
	copy(packetNew[len(packetNew)-2:], packet[len(packet)-2:])

	return packetNew, nil
}

// ParsePacket phân tích packet để lấy opcode và payload
func ParsePacket(data []byte) (byte, []byte, error) {
	if len(data) < 5 {
		return 0, nil, errors.New("packet ParsePacket2 quá ngắn")
	}

	if data[0] == 0xAA && data[1] == 0x55 {
		if len(data) < 10 {
			return 0, nil, errors.New("packet ParsePacket3 quá ngắn cho game server packet")
		}

		opcode := data[8]
		if len(data) <= 14 {
			return opcode, []byte{}, nil
		}
		payload := data[12 : len(data)-2]
		return opcode, payload, nil
	} else {
		opcode := data[0]
		payload := data[1:]
		return opcode, payload, nil
	}
}

// BytesToHex chuyển đổi mảng byte thành chuỗi hex
func BytesToHex(data []byte) string {
	var buffer bytes.Buffer
	for i, b := range data {
		if i > 0 {
			buffer.WriteString("-")
		}
		buffer.WriteString(fmt.Sprintf("%02X", b))
	}
	return buffer.String()
}
