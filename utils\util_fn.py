import re
import struct

def get_type(data):
    try:
        adjusted_data = bytearray(data)
        request_type = struct.unpack('<H', adjusted_data[8:10])[0]
        return request_type
    except Exception as e:
        print(f"Error processing packet getType: {e} - {len(data)}")
        return None


def manage_login_packet(data):
    try:
        adjusted_data = bytearray(data)

        # Extract wordid (adjusted_data[0:2])
      #  length = struct.unpack('<H', adjusted_data[4:6])[0]

        wordid = struct.unpack('<H', adjusted_data[2:4])[0]
        # Extract aa (adjusted_data[2:4])
        # Extract request_type
        request_type = struct.unpack('<H', adjusted_data[0:2])[0]
       # length_mstream = struct.unpack('<H', adjusted_data[8:10])[0]

        return request_type, wordid
    except Exception as e:
        print(f"Error processing packet manage Packet: {e} - {len(data)}")
        return None, None

def manage_packet(data):
    try:
        adjusted_data = bytearray(data)

        # Extract wordid (adjusted_data[0:2])
      #  length = struct.unpack('<H', adjusted_data[4:6])[0]

        wordid = struct.unpack('<H', adjusted_data[6:8])[0]
        # Extract aa (adjusted_data[2:4])
        # Extract request_type
        request_type = struct.unpack('<H', adjusted_data[8:10])[0]
       # length_mstream = struct.unpack('<H', adjusted_data[8:10])[0]

        return request_type, wordid
    except Exception as e:
        print(f"Error processing packet manage Packet: {e} - {len(data)}")
        return None, None
def manage_packet2(data):
    try:
        adjusted_data = bytearray(data)
        # Extract wordid (adjusted_data[0:2])
        length = struct.unpack('<H', adjusted_data[4:6])[0]

        wordid = struct.unpack('<H', adjusted_data[6:8])[0]
        # Extract aa (adjusted_data[2:4])
        # Extract request_type
        request_type = struct.unpack('<H', adjusted_data[8:10])[0]
        length_mstream = struct.unpack('<H', adjusted_data[10:12])[0]

        return request_type, wordid,length_mstream,length
    except Exception as e:
        print(f"Error processing packet manage paket2: {e} - {len(data)}")
        return None, None, None
    
import struct

def unpack_bytes(adjusted_data):
    # Initialize an empty list to store unpacked values
    fld_map_values = []

    # Iterate over the adjusted_data in steps of 2 bytes
    for offset in range(0, len(adjusted_data), 2):
        # Unpack 2 bytes from adjusted_data at the current offset
        if (len(adjusted_data) - offset < 2):
            break
        fld_map_value = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
        # Append the unpacked value to fld_map_values
        fld_map_values.append(fld_map_value)

    return fld_map_values

def decrypta(buff):
    Key2 = 2138432278
    g_cur_key = bytearray([
        4, 55, 194, 8, 236, 235, 63, 151, 52, 131,
        91, 17, 67, 248, 106, 144, 193, 231, 211, 232,
        92, 237, 241, 255, 89, 139, 13, 129, 51, 65,
        89, 137
    ])
    
    b = 0
    b2 = 0
    for i in range(len(buff) - 6):
        b2 = buff[4 + i]
        buff[4 + i] = buff[4 + i] ^ g_cur_key[i % 8] ^ b
        b = b2
def read1(data,offset):
    return struct.unpack("<B",data[offset:offset+1])[0]
def read4(data,offset):
    return struct.unpack("<I", data[offset:offset+4])[0]
def read2(data,offset):
    return struct.unpack("<H", data[offset:offset+2])[0]
def readfloat(data,offset):
    return struct.unpack("<f",data[offset:offset+4])[0]
def readCharacter(data,offset,length):
    return data[offset:offset+length].decode('ascii').replace('\x00', '')

def split_packets(input_string):
    # Define the regex pattern to match strings starting with AA55 and ending with 55AA
    pattern = re.compile(r'(AA55.*?55AA)', re.IGNORECASE)
    
    # Find all matches in the input string
    packets = pattern.findall(input_string)
    
    return packets

def decode_packet(encrypted_packet):
    try:
        if len(encrypted_packet) < 15:
            raise ValueError("Invalid packet length")
       
        """ if (
            encrypted_packet[0] != 0xAA
            or encrypted_packet[1] != 0x55
            or encrypted_packet[-2] != 0x55
            or encrypted_packet[-1] != 0xAA
        ):
            raise ValueError("Invalid packet header/footer") """
        
        adjusted_data = bytearray(encrypted_packet)
        offset = 6
        wordid = read2(adjusted_data,offset)
        request_type = read2(adjusted_data,offset+2)
        request_length = read2(adjusted_data,offset+4)
        offset +=6
        if request_type == 9 :
            offset = 12
            target_Id = read4(adjusted_data,offset)
            skillId = read4(adjusted_data,offset+4)
            xx = readfloat(adjusted_data,offset+8)
            yy = readfloat(adjusted_data,offset+12)
            print(f"Attack {target_Id} with {skillId} in {xx} - {yy}")
            return {
                "wordid": wordid,
                "request_type": request_type,
               
            }
        if request_type == 5440:
            print("Status Effect")
            offset= 20
            status_id = read4(adjusted_data,offset)
            switch_on = read4(adjusted_data,offset+4)
            offset= 60
            status_id2 = read4(adjusted_data,offset)
            switch_one2 = read4(adjusted_data,offset+4)
            status_time = read4(adjusted_data,40)
            status_amount = read4(adjusted_data,44)
            return {
                "wordid": wordid,
                "request_type": request_type,
               
            }
        if request_type == 103:
            npc_count = read4(adjusted_data,offset)
            print(f"npc_count: {npc_count}")
            offset+=4
            npc_data_list = []

            for _ in range(npc_count):
                fld_index1 = read4(adjusted_data,offset)
                fld_index2 = read4(adjusted_data,offset+4)
                offset += 8

                fld_pid = read2(adjusted_data,offset)
                offset += 2

                npc_death = read2(adjusted_data,offset)
                offset += 2

                npc_hp = read4(adjusted_data,offset)
                npc_hp_max = read4(adjusted_data,offset+4)
                offset += 8

                fld_x = readfloat(adjusted_data,offset)
                fld_z = readfloat(adjusted_data,offset+4)
                fld_y = readfloat(adjusted_data,offset+8)
                offset += 12

                offset += 4

                fld_face1 = readfloat(adjusted_data,offset)
                fld_face2 = readfloat(adjusted_data,offset+4)
                offset += 8

                fld_x2 = readfloat(adjusted_data,offset)
                fld_z2 = readfloat(adjusted_data,offset+4)
                fld_y2 = readfloat(adjusted_data,offset+8)
                offset += 12

                fld_boss = read4(adjusted_data,offset)
                offset += 4

                boss_flag = read4(adjusted_data,offset)
                offset += 4

                unknown1 = read4(adjusted_data,offset)
                map_flag = read4(adjusted_data,offset)
                offset += 8

                unknown2 = read4(adjusted_data,offset)
                max_value = read4(adjusted_data,offset)
                offset += 8

                npc_data = {
                    "fld_index1": fld_index1,
                    "fld_index2": fld_index2,
                    "fld_pid": fld_pid,
                    "npc_death": npc_death,
                    "npc_hp": npc_hp,
                    "npc_hp_max": npc_hp_max,
                    "fld_x": fld_x,
                    "fld_z": fld_z,
                    "fld_y": fld_y,
                    "fld_face1": fld_face1,
                    "fld_face2": fld_face2,
                    "fld_x2": fld_x2,
                    "fld_z2": fld_z2,
                    "fld_y2": fld_y2,
                    "fld_boss": fld_boss,
                    "boss_flag": boss_flag,
                    "unknown1": unknown1,
                    "map_flag": map_flag,
                    "unknown2": unknown2,
                    "max_value": max_value,
                }

                npc_data_list.append(npc_data)

            return {
                "wordid": wordid,
                "request_type": request_type,
                "npc_data_list": npc_data_list,
            }
        if request_type == 209:
            # npcmove
            fld_x = read2(adjusted_data,offset)
            offset+=2
            fld_y = read2(adjusted_data,offset)
            offset+=2
            fld_z = read2(adjusted_data,offset)
            offset+=2
            fld_map = read2(adjusted_data,offset)
            offset+=2
            mapdata = {
                "wordid": wordid, 
                "request_type": request_type,
                "fld_x": fld_x,
                "fld_y": fld_y,
                "fld_z" : fld_z,
                "fld_map" : fld_map
            }
            return mapdata
        if request_type == 72:
            #Death to city
            print("handle map changer")
            move_type = read1(adjusted_data,offset)
            print(move_type)
            movedata = {
                "wordid": wordid,
                "request_type":request_type,
                "move_type":move_type,
            }
            match move_type:
                case 88:
                    # Teleporter_move change map
                    numcase_2 = struct.unpack("<I",adjusted_data[offset+14:offset+18])[0]
                    movedata["fld_map"] = numcase_2
                    return movedata
                case 99:
                    numcase_2 = struct.unpack("<H",adjusted_data[offset+16:offset+18])[0]
                    movedata["fld_map"] = numcase_2
                    return movedata
                case _:
                    return movedata
        if request_type == 121:
            print("Changing Map using item")
            offset = 28
            map_packet = read2(adjusted_data,offset)
            movedata = {
                "wordid": wordid,
                "request_type":request_type,
            }
            if map_packet:
                movedata["fld_map"] = map_packet
                return movedata
            return movedata
        
        if request_type == 584:
            fld_index3 = struct.unpack("<I", adjusted_data[offset: offset + 4])[0]
            fld_index4 = struct.unpack("<I", adjusted_data[offset + 4: offset +8])[0]
            fld_index5 = struct.unpack("<I", adjusted_data[offset + 8: offset +12])[0]
            offset += 12
            fld_index1 = struct.unpack("<I", adjusted_data[offset: offset + 4])[0]
            fld_index2 = struct.unpack("<I", adjusted_data[offset + 4: offset +8])[0]
            offset += 8
            fld_pid = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_death = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_hp = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 6

            fld_x = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
            fld_z = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[0]
            fld_y = struct.unpack("<f", adjusted_data[offset + 8 : offset + 12])[0]
            npc_data = {
                "fld_index3": fld_index3,
                "fld_index4": fld_index4,
                "fld_index5": fld_index5,
                "fld_index1": fld_index1,
                "fld_index2": fld_index2,
                "fld_pid": fld_pid,
                "npc_death": npc_death,
                "npc_hp": npc_hp,
                "fld_x": fld_x,
                "fld_z": fld_z,
                "fld_y": fld_y,
            }


            return {
                "wordid": wordid,
                "request_type": request_type,
                "npc_data_list": npc_data,
            }
        if request_type != 0:
            fld_map_values = unpack_bytes(adjusted_data)
            movedata = {
                "wordid": wordid,
                "request_type":request_type,
                "fld_map": fld_map_values
            }
            return movedata
        return {"wordid": wordid, "request_type": request_type}

    except Exception as e:
        print(f"Error decoding packet: {e}")
        return None
