package main

import (
	"fmt"
	"os"
	"runtime"

	"1l4e/neo_packer/internal/ui"
)

func main() {
	// Check for root/admin privileges on Linux/macOS
	if runtime.GOOS == "linux" || runtime.GOOS == "darwin" {
		if os.Geteuid() != 0 {
			fmt.Println("Warning: Neo Packer requires root privileges to capture packets.")
			fmt.Println("Please run with sudo or as root.")
			fmt.Println("Alternatively, you can grant CAP_NET_RAW capability to the binary:")
			fmt.Println("sudo setcap cap_net_raw=eip /path/to/neo_packer")
			fmt.Println("\nContinuing without packet capture capabilities...")
		}
	}

	// Create and run UI
	app := ui.NewUI()
	app.Run()
}
