import struct

from util_fn import read2


def unpack_bytes1(adjusted_data):
    # Initialize an empty list to store unpacked values
    fld_map_values = []
    adjusted_data = adjusted_data[1:-1]
    # Iterate over the adjusted_data in steps of 2 bytes
    for offset in range(0, len(adjusted_data), 2):
        # Unpack 2 bytes from adjusted_data at the current offset
        if len(adjusted_data) - offset < 2:
            break
        fld_map_value = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
        # Append the unpacked value to fld_map_values
        fld_map_values.append(fld_map_value)

    return fld_map_values


def unpack_bytes(adjusted_data):
    # Initialize an empty list to store unpacked values
    fld_map_values = []

    # Iterate over the adjusted_data in steps of 2 bytes
    for offset in range(0, len(adjusted_data), 2):
        # Unpack 2 bytes from adjusted_data at the current offset
        if len(adjusted_data) - offset < 2:
            break
        fld_map_value = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
        # Append the unpacked value to fld_map_values
        fld_map_values.append(fld_map_value)

    return fld_map_values


def decode_packet(encrypted_packet):
    try:
        # if len(encrypted_packet) < 15:
        #    raise ValueError("Invalid packet length")
        """
        if (
            encrypted_packet[0] != 0xAA
            or encrypted_packet[1] != 0x55
            or encrypted_packet[-2] != 0x55
            or encrypted_packet[-1] != 0xAA
        ):
            raise ValueError("Invalid packet header/footer")
        """
        adjusted_data = bytearray(encrypted_packet)
        offset = 6
        wordid = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
        request_type = struct.unpack("<H", adjusted_data[offset + 2 : offset + 4])[0]
        request_length = struct.unpack("<H", adjusted_data[offset + 4 : offset + 6])[0]
        offset += 6
        print(request_length)
        if request_type == 103:
            npc_count = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
            print(f"npc_count: {npc_count}")
            offset += 4  # 14
            npc_data_list = []

            for _ in range(npc_count):
                fld_index1 = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                fld_index2 = struct.unpack(
                    "<I", adjusted_data[offset + 4 : offset + 8]
                )[0]
                offset += 8  # 18

                fld_pid = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
                offset += 2

                npc_death = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
                offset += 2

                npc_hp = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                npc_hp_max = struct.unpack(
                    "<I", adjusted_data[offset + 4 : offset + 8]
                )[0]
                offset += 8  # 26

                fld_x = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
                fld_z = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[0]
                fld_y = struct.unpack("<f", adjusted_data[offset + 8 : offset + 12])[0]
                offset += 12

                offset += 4

                fld_face1 = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
                fld_face2 = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[
                    0
                ]
                offset += 8

                fld_x2 = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
                fld_z2 = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[0]
                fld_y2 = struct.unpack("<f", adjusted_data[offset + 8 : offset + 12])[0]
                offset += 12

                fld_boss = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                offset += 4

                boss_flag = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                offset += 4

                unknown1 = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                map_flag = struct.unpack("<I", adjusted_data[offset + 4 : offset + 8])[
                    0
                ]
                offset += 8

                unknown2 = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                max_value = struct.unpack("<I", adjusted_data[offset + 4 : offset + 8])[
                    0
                ]
                offset += 8

                npc_data = {
                    "fld_index1": fld_index1,
                    "fld_index2": fld_index2,
                    "fld_pid": fld_pid,
                    "npc_death": npc_death,
                    "npc_hp": npc_hp,
                    "npc_hp_max": npc_hp_max,
                    "fld_x": fld_x,
                    "fld_z": fld_z,
                    "fld_y": fld_y,
                    "fld_face1": fld_face1,
                    "fld_face2": fld_face2,
                    "fld_x2": fld_x2,
                    "fld_z2": fld_z2,
                    "fld_y2": fld_y2,
                    "fld_boss": fld_boss,
                    "boss_flag": boss_flag,
                    "unknown1": unknown1,
                    "map_flag": map_flag,
                    "unknown2": unknown2,
                    "max_value": max_value,
                }

                npc_data_list.append(npc_data)

            return {
                "wordid": wordid,
                "request_type": request_type,
                "npc_data_list": npc_data_list,
            }
        if request_type == 209:
            # npcmove
            fld_x = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2
            fld_y = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2
            fld_z = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2
            fld_map = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2
            mapdata = {
                "wordid": wordid,
                "request_type": request_type,
                "fld_x": fld_x,
                "fld_y": fld_y,
                "fld_z": fld_z,
                "fld_map": fld_map,
            }
            return mapdata
        if request_type == 72:
            # Death to city
            print("handle map changer")
            move_type = struct.unpack("<B", adjusted_data[offset : offset + 1])[0]
            print(move_type)
            movedata = {
                "wordid": wordid,
                "request_type": request_type,
                "move_type": move_type,
            }
            match move_type:
                case 88:
                    # Teleporter_move change map
                    numcase_2 = struct.unpack(
                        "<I", adjusted_data[offset + 14 : offset + 18]
                    )[0]
                    movedata["fld_map"] = numcase_2
                    return movedata
                case 99:
                    numcase_2 = struct.unpack(
                        "<H", adjusted_data[offset + 16 : offset + 18]
                    )[0]
                    movedata["fld_map"] = numcase_2
                    return movedata
                case _:
                    return movedata
        if request_type == 121:
            print("Changing Map using item")
            offset = 28
            map_packet = read2(adjusted_data, offset)
            movedata = {
                "wordid": wordid,
                "request_type": request_type,
            }
            if map_packet:
                movedata["fld_map"] = map_packet
                return movedata
            return movedata
        if request_type == 576:
            offset = 15
            hex_to_find = "00007041"
            hex_string = encrypted_packet.hex()
            index = hex_string.find(hex_to_find)
            if index == -1:
                return
            offset2 = 14
            offset = index // 2 - 18 - offset2
            fld_index = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += offset2
            offset += 4
            fld_pid = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_death = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_hp = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 6

            fld_x = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
            fld_z = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[0]
            fld_y = struct.unpack("<f", adjusted_data[offset + 8 : offset + 12])[0]
            #print(fld_index, fld_pid, npc_death, npc_hp, fld_x, fld_z, fld_y)
            if fld_pid == 0 or npc_death != 1:
                return
            return {
                "fld_index1": fld_index,
                "fld_index2": fld_index,
                "fld_pid": fld_pid,
                "npc_death": npc_death,
                "npc_hp": npc_hp,
                "npc_hp_max": npc_hp,
                "fld_x": fld_x,
                "fld_z": fld_z,
                "fld_y": fld_y,
                "fld_face1": 0,
                "fld_face2": 0,
                "fld_x2": fld_x,
                "fld_z2": fld_z,
                "fld_y2": fld_y,
                "fld_boss": 0,
                "boss_flag": 0,
                "unknown1": 0,
                "map_flag": 0,
                "unknown2": 0,
                "max_value": 0,
            }

        if request_type == 584:
            print("584")
            hex_to_find = "00007041"
            hex_string = encrypted_packet.hex()
            index = hex_string.find(hex_to_find)
            if index == -1:
                print("hex not found")
                return
            offset2 = 1
            offset = index // 2 - 18 - offset2
            fld_index = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += offset2
            offset += 4
            fld_pid = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_death = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_hp = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 6

            fld_x = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
            fld_z = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[0]
            fld_y = struct.unpack("<f", adjusted_data[offset + 8 : offset + 12])[0]
            print(fld_index, fld_pid, npc_death, npc_hp, fld_x, fld_z, fld_y)
            if fld_pid == 0 or npc_death != 1:
                print("invalid data")
                return
            return {
                "fld_index1": fld_index,
                "fld_index2": fld_index,
                "fld_pid": fld_pid,
                "npc_death": npc_death,
                "npc_hp": npc_hp,
                "npc_hp_max": npc_hp,
                "fld_x": fld_x,
                "fld_z": fld_z,
                "fld_y": fld_y,
                "fld_face1": 0,
                "fld_face2": 0,
                "fld_x2": fld_x,
                "fld_z2": fld_z,
                "fld_y2": fld_y,
                "fld_boss": 0,
                "boss_flag": 0,
                "unknown1": 0,
                "map_flag": 0,
                "unknown2": 0,
                "max_value": 0,
            }
        if request_type == 903:
            print("903")
            hex_to_find = "1127"
            hex_string = encrypted_packet.hex()
            index = hex_string.find(hex_to_find)
            if index == -1:
                print("hex not found")
                return
            offset2 = 1
            offset = index // 2 - 18 - offset2
            print(index,offset)
            fld_index = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += offset2
            offset += 4
            fld_pid = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_death = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_hp = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 6

            fld_x = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
            fld_z = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[0]
            fld_y = struct.unpack("<f", adjusted_data[offset + 8 : offset + 12])[0]
            print(fld_index, fld_pid, npc_death, npc_hp, fld_x, fld_z, fld_y)
            if fld_pid == 0 or npc_death != 1:
                print("invalid data")
                return
            return {
                "fld_index1": fld_index,
                "fld_index2": fld_index,
                "fld_pid": fld_pid,
                "npc_death": npc_death,
                "npc_hp": npc_hp,
                "npc_hp_max": npc_hp,
                "fld_x": fld_x,
                "fld_z": fld_z,
                "fld_y": fld_y,
                "fld_face1": 0,
                "fld_face2": 0,
                "fld_x2": fld_x,
                "fld_z2": fld_z,
                "fld_y2": fld_y,
                "fld_boss": 0,
                "boss_flag": 0,
                "unknown1": 0,
                "map_flag": 0,
                "unknown2": 0,
                "max_value": 0,
            }
        if request_type != 0:
            fld_map_values = unpack_bytes(adjusted_data)
            fld_map_values2 = unpack_bytes1(adjusted_data)
            movedata = {
                "wordid": wordid,
                "request_type": request_type,
                "fld_map": fld_map_values,
                "fld_map2": fld_map_values2,
            }
            return movedata
        return {"wordid": wordid, "request_type": request_type}

    except Exception as e:
        print(f"Error decoding packet: {e}")
        return None


""" hexdata = "aa550c02000000008703040202000100fbc773dd21c0dfaf4702000000000000c4f387f2fa0000000100000086c53f5a04000000040000000fda8b3b2aee3307bc6b3a44c94f79fc3f8619138c34f00a55b94fcba5f9f65eb804d95cbec6cd5ff03f55e1af377099d7b8d43c0000000025e5409f6d35c1050f384d4b31dbf8edee17003e37e6c63efe7c8deece136e65ca4d573c046e09c2f42f864adae9df826e481a540662287d0e0fc5eade0d170effa07da0bec14d969962d390768da841e1fb122b06d820f527928c995f7f19aee71d083ec3608af04b4b9ec0b3a601d96787442780e5e3cc13a4ce1bf59d9964700d48bacf7401b50990988d3c9221f2395401c432f3f1776e326be9000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000055aa"
packet = bytes.fromhex(hexdata)

decoded = decode_packet(packet)
print(decoded)
 """