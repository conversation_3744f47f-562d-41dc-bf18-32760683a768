#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from Crypto.Cipher import AES, ARC4
import binascii

# Key nhận được từ RSA decryption (32 bytes)
AES_KEY_HEX = "74C3A8EB9448B68C386C5A085AC583969F57F7B0CEC191955B0553568BEBF1FB"

# Packet opcode 1 data
PACKET_HEX = (
    "aa558a000000000001008000a32a21b3d427ceacceb862efa1396f133a5a7fdfae4def11f850610ea0487b29d35937104b4ae29623114af73906712b44379c73400af2c217315030d5b250ab839b2eb57186d95248f80692458e257ed928488cd5cba53adea1809c564e801c65a8ab939f8b1e97d6dbe625c50e00cf6d29d21490c042077ac24da9a1c6f48f55aa"
)

def parse_packet(packet_hex):
    """Parse cấu trúc packet"""
    packet = binascii.unhexlify(packet_hex)

    if len(packet) < 12:
        raise ValueError("Packet quá ngắn")

    # Parse header
    magic1 = packet[0:2]  # aa55
    length = int.from_bytes(packet[2:4], 'little')  # 8a00 = 138
    session_id = packet[4:8]  # 00000000
    opcode = int.from_bytes(packet[8:10], 'little')  # 0100 = 1
    payload_len = int.from_bytes(packet[10:12], 'little')  # 8000 = 128

    # Extract payload (encrypted data)
    payload = packet[12:12+payload_len]
    magic2 = packet[12+payload_len:12+payload_len+2]  # 55aa

    print(f"📦 PACKET STRUCTURE:")
    print(f"   Magic1: {magic1.hex()}")
    print(f"   Length: {length}")
    print(f"   Session ID: {session_id.hex()}")
    print(f"   Opcode: {opcode}")
    print(f"   Payload Length: {payload_len}")
    print(f"   Magic2: {magic2.hex()}")
    print(f"   Payload (hex): {payload.hex()}")

    return payload


def remove_pkcs7_padding(data):
    """Loại bỏ PKCS7 padding"""
    if len(data) == 0:
        return data

    padding_len = data[-1]
    if padding_len > len(data) or padding_len == 0:
        return data

    # Kiểm tra tất cả padding bytes có giống nhau không
    for i in range(len(data) - padding_len, len(data)):
        if data[i] != padding_len:
            return data

    return data[:-padding_len]


def try_aes_ecb(key, payload):
    """Thử giải mã AES-256-ECB"""
    try:
        cipher = AES.new(key, AES.MODE_ECB)
        plaintext = cipher.decrypt(payload)

        # Thử loại bỏ PKCS7 padding
        unpadded = remove_pkcs7_padding(plaintext)

        return plaintext, unpadded
    except Exception as e:
        return None, None


def try_aes_cbc(key, payload, iv=None):
    """Thử giải mã AES-256-CBC"""
    if iv is None:
        iv = b'\x00' * 16  # IV = 16 bytes zero

    try:
        cipher = AES.new(key, AES.MODE_CBC, iv)
        plaintext = cipher.decrypt(payload)

        # Thử loại bỏ PKCS7 padding
        unpadded = remove_pkcs7_padding(plaintext)

        return plaintext, unpadded
    except Exception as e:
        return None, None


def try_rc4(key, payload):
    """Thử giải mã RC4"""
    try:
        cipher = ARC4.new(key)
        plaintext = cipher.decrypt(payload)
        return plaintext
    except Exception as e:
        return None


def analyze_plaintext(data, method_name):
    """Phân tích plaintext để xem có hợp lý không"""
    if data is None:
        return False

    print(f"\n🔓 {method_name}:")
    print(f"   Hex: {data.hex()}")

    # Kiểm tra ASCII printable
    ascii_count = sum(1 for b in data if 32 <= b <= 126)
    ascii_ratio = ascii_count / len(data) if len(data) > 0 else 0

    # Kiểm tra null bytes
    null_count = data.count(0)
    null_ratio = null_count / len(data) if len(data) > 0 else 0

    print(f"   ASCII ratio: {ascii_ratio:.2%}")
    print(f"   Null ratio: {null_ratio:.2%}")

    # Thử hiển thị như ASCII (thay thế non-printable bằng .)
    ascii_repr = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data)
    print(f"   ASCII: {ascii_repr}")

    # Tìm chuỗi ASCII dài nhất
    current_str = ""
    longest_str = ""
    for b in data:
        if 32 <= b <= 126:
            current_str += chr(b)
        else:
            if len(current_str) > len(longest_str):
                longest_str = current_str
            current_str = ""
    if len(current_str) > len(longest_str):
        longest_str = current_str

    if len(longest_str) >= 3:
        print(f"   Longest ASCII string: '{longest_str}'")

    # Heuristic: có thể là plaintext hợp lý nếu:
    # - ASCII ratio > 30% HOẶC
    # - Có chuỗi ASCII dài >= 4 chars HOẶC
    # - Null ratio cao (có thể là C-style strings với padding)
    is_likely_valid = (ascii_ratio > 0.3 or
                      len(longest_str) >= 4 or
                      (null_ratio > 0.3 and ascii_ratio > 0.1))

    if is_likely_valid:
        print(f"   ✅ Có vẻ là plaintext hợp lý!")
        return True
    else:
        print(f"   ❌ Có vẻ vẫn là dữ liệu mã hóa/rác")
        return False


def try_decrypt_username(key, username_bytes, session_id=0):
    """Thử giải mã 16 bytes đầu (username) với nhiều phương pháp"""
    import hashlib

    print(f"\n🔓 GIẢI MÃ USERNAME (16 bytes đầu):")
    print(f"   Encrypted: {username_bytes.hex()}")
    print(f"   Session ID: {session_id}")

    methods = []

    # === KEY DERIVATION METHODS ===

    # 1. MD5 của RSA key
    try:
        md5_key = hashlib.md5(key).digest()  # 16 bytes
        cipher = AES.new(md5_key, AES.MODE_ECB)
        result = cipher.decrypt(username_bytes)
        methods.append(("AES-128-ECB (MD5 of RSA key)", result))
    except:
        pass

    # 2. SHA1 của RSA key (lấy 16 bytes đầu)
    try:
        sha1_key = hashlib.sha1(key).digest()[:16]  # 16 bytes đầu
        cipher = AES.new(sha1_key, AES.MODE_ECB)
        result = cipher.decrypt(username_bytes)
        methods.append(("AES-128-ECB (SHA1 of RSA key)", result))
    except:
        pass

    # 3. SHA256 của RSA key (lấy 16 bytes đầu)
    try:
        sha256_key = hashlib.sha256(key).digest()[:16]  # 16 bytes đầu
        cipher = AES.new(sha256_key, AES.MODE_ECB)
        result = cipher.decrypt(username_bytes)
        methods.append(("AES-128-ECB (SHA256 of RSA key)", result))
    except:
        pass

    # === SESSION-BASED METHODS ===

    # 4. RSA key XOR với session ID
    try:
        session_bytes = session_id.to_bytes(4, 'little') * 4  # Repeat để có 16 bytes
        derived_key = bytes(a ^ b for a, b in zip(key[:16], session_bytes))
        cipher = AES.new(derived_key, AES.MODE_ECB)
        result = cipher.decrypt(username_bytes)
        methods.append(("AES-128-ECB (RSA key XOR session)", result))
    except:
        pass

    # 5. MD5(RSA key + session ID)
    try:
        combined = key + session_id.to_bytes(4, 'little')
        md5_combined = hashlib.md5(combined).digest()
        cipher = AES.new(md5_combined, AES.MODE_ECB)
        result = cipher.decrypt(username_bytes)
        methods.append(("AES-128-ECB (MD5 of RSA+session)", result))
    except:
        pass

    # === SIMPLE METHODS ===

    # 6. First 16 bytes của RSA key
    try:
        aes128_key = key[:16]
        cipher = AES.new(aes128_key, AES.MODE_ECB)
        result = cipher.decrypt(username_bytes)
        methods.append(("AES-128-ECB (first 16 bytes)", result))
    except:
        pass

    # 7. Last 16 bytes của RSA key
    try:
        aes128_key = key[16:32]
        cipher = AES.new(aes128_key, AES.MODE_ECB)
        result = cipher.decrypt(username_bytes)
        methods.append(("AES-128-ECB (last 16 bytes)", result))
    except:
        pass

    # 8. XOR với MD5 của RSA key
    try:
        md5_key = hashlib.md5(key).digest()
        result = bytes(a ^ b for a, b in zip(username_bytes, md5_key))
        methods.append(("XOR with MD5(RSA key)", result))
    except:
        pass

    # 9. RC4 với MD5 của RSA key
    try:
        md5_key = hashlib.md5(key).digest()
        cipher = ARC4.new(md5_key)
        result = cipher.decrypt(username_bytes)
        methods.append(("RC4 with MD5(RSA key)", result))
    except:
        pass

    # === USERNAME HASH METHODS ===

    # 10. Thử giả sử username đã được hash trước khi mã hóa
    known_usernames = ["ybtest", "admin", "test", "user", "player"]
    for username in known_usernames:
        try:
            # MD5 của username
            username_md5 = hashlib.md5(username.encode()).digest()
            if username_md5 == username_bytes:
                methods.append(("Plain MD5 hash", username.encode().ljust(16, b'\x00')))

            # Thử giải mã với assumption rằng đây là MD5(username) được mã hóa
            for key_variant in [key[:16], hashlib.md5(key).digest()]:
                try:
                    cipher = AES.new(key_variant, AES.MODE_ECB)
                    decrypted = cipher.decrypt(username_bytes)
                    if decrypted == username_md5:
                        methods.append((f"AES-ECB encrypted MD5('{username}')", username.encode().ljust(16, b'\x00')))
                except:
                    pass
        except:
            pass

    # === ADDITIONAL METHODS ===

    # 11. Thử DES/3DES (nếu có thể)
    try:
        from Crypto.Cipher import DES3
        # Dùng 24 bytes đầu của key cho 3DES
        des3_key = key[:24]
        cipher = DES3.new(des3_key, DES3.MODE_ECB)
        result = cipher.decrypt(username_bytes[:8] + username_bytes[8:16])  # 3DES cần 8-byte blocks
        methods.append(("3DES-ECB", result))
    except:
        pass

    # 12. Thử Blowfish
    try:
        from Crypto.Cipher import Blowfish
        bf_key = key[:16]  # Blowfish key có thể từ 4-56 bytes
        cipher = Blowfish.new(bf_key, Blowfish.MODE_ECB)
        result = cipher.decrypt(username_bytes)
        methods.append(("Blowfish-ECB", result))
    except:
        pass

    # 13. Thử với key được rotate/shift
    try:
        # Rotate key 1 byte
        rotated_key = key[1:17]
        cipher = AES.new(rotated_key, AES.MODE_ECB)
        result = cipher.decrypt(username_bytes)
        methods.append(("AES-128-ECB (rotated key)", result))
    except:
        pass

    # 14. Thử với key được bit-shift
    try:
        # XOR key với constant
        xor_key = bytes(b ^ 0x5A for b in key[:16])  # XOR với 0x5A
        cipher = AES.new(xor_key, AES.MODE_ECB)
        result = cipher.decrypt(username_bytes)
        methods.append(("AES-128-ECB (XOR key with 0x5A)", result))
    except:
        pass

    # 15. Thử simple substitution cipher
    try:
        # Caesar cipher với key bytes
        result = bytearray()
        for i, b in enumerate(username_bytes):
            shift = key[i % len(key)]
            result.append((b - shift) % 256)
        methods.append(("Caesar cipher (key-based)", bytes(result)))
    except:
        pass

    # 16. Thử với username được pad theo cách khác
    known_usernames_padded = [
        "ybtest".ljust(16, '\x00'),  # null padding
        "ybtest".ljust(16, ' '),     # space padding
        "ybtest".rjust(16, '\x00'),  # right-aligned
        ("ybtest" * 3)[:16],         # repeat
    ]

    for padded_username in known_usernames_padded:
        padded_bytes = padded_username.encode() if isinstance(padded_username, str) else padded_username
        if len(padded_bytes) == 16:
            # Thử encrypt với các key variants để xem có match không
            for key_name, test_key in [
                ("MD5(RSA)", hashlib.md5(key).digest()),
                ("First16", key[:16]),
                ("Last16", key[16:32]),
            ]:
                try:
                    cipher = AES.new(test_key, AES.MODE_ECB)
                    encrypted = cipher.encrypt(padded_bytes)
                    if encrypted == username_bytes:
                        methods.append((f"REVERSE: '{padded_username}' encrypted with {key_name}", padded_bytes))
                except:
                    pass

    # === ANALYSIS ===
    print(f"\n📊 Thử {len(methods)} phương pháp:")

    best_candidates = []

    for method, result in methods:
        print(f"\n   {method}:")
        print(f"      Hex: {result.hex()}")
        ascii_repr = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in result)
        print(f"      ASCII: '{ascii_repr}'")

        # Phân tích chi tiết
        score = 0
        analysis = []

        try:
            # Kiểm tra null-terminated string
            null_pos = result.find(b'\x00')
            if null_pos > 0:
                text = result[:null_pos].decode('ascii', errors='ignore')
                analysis.append(f"null-term: '{text}'")
                if 'ybtest' in text.lower():
                    score += 100
                    analysis.append("✅ CONTAINS YBTEST!")
                elif text.isalnum() and len(text) >= 3:
                    score += 50
                    analysis.append(f"valid username candidate")

            # Kiểm tra toàn bộ như ASCII
            full_text = result.decode('ascii', errors='ignore').rstrip('\x00')
            if 'ybtest' in full_text.lower():
                score += 100
                analysis.append("✅ FULL TEXT CONTAINS YBTEST!")
            elif full_text.isalnum() and len(full_text) >= 3:
                score += 30
                analysis.append(f"possible username: '{full_text}'")

            # Kiểm tra entropy (random data có entropy cao)
            unique_bytes = len(set(result))
            if unique_bytes < 8:  # Low entropy = có thể là text với padding
                score += 20
                analysis.append(f"low entropy ({unique_bytes} unique bytes)")

        except:
            pass

        if analysis:
            print(f"      Analysis: {', '.join(analysis)}")

        if score > 0:
            best_candidates.append((score, method, result, analysis))

    # Hiển thị candidates tốt nhất
    if best_candidates:
        best_candidates.sort(reverse=True)
        print(f"\n🏆 TOP CANDIDATES:")
        for i, (score, method, result, analysis) in enumerate(best_candidates[:3], 1):
            print(f"   {i}. {method} (score: {score})")
            print(f"      Result: {result.hex()}")
            print(f"      ASCII: '{result.decode('ascii', errors='ignore').rstrip(chr(0))}'")
    else:
        print(f"\n❌ Không tìm thấy candidate khả thi nào")
        print(f"💡 Có thể username được mã hóa bằng thuật toán khác hoặc có preprocessing đặc biệt")


def check_username_hashes():
    """Kiểm tra xem 16 bytes đầu có phải là hash của username không"""
    import hashlib

    print("\n🔍 KIỂM TRA USERNAME HASH:")

    # Các username có thể
    usernames = ["ybtest", "admin", "test", "user", "player", "guest"]

    # Các hash từ log server
    known_hashes = [
        "5105CA518306A5BBF66E349627E9FA10",  # Session 1008
        "C4368A6D45456C56D1F69AB23667A009",  # Session 1009
        "a32a21b3d427ceacceb862efa1396f13",  # Current packet
    ]

    print("Known hashes from server logs:")
    for i, h in enumerate(known_hashes, 1):
        print(f"   {i}. {h}")

    print("\nTesting username hashes:")

    for username in usernames:
        # MD5
        md5_hash = hashlib.md5(username.encode()).hexdigest().upper()
        # SHA1 (first 16 bytes)
        sha1_hash = hashlib.sha1(username.encode()).hexdigest()[:32].upper()
        # SHA256 (first 16 bytes)
        sha256_hash = hashlib.sha256(username.encode()).hexdigest()[:32].upper()

        print(f"\n   Username: '{username}'")
        print(f"      MD5:    {md5_hash}")
        print(f"      SHA1:   {sha1_hash}")
        print(f"      SHA256: {sha256_hash}")

        # Kiểm tra match
        for hash_val in [md5_hash, sha1_hash, sha256_hash]:
            if hash_val in known_hashes:
                print(f"      ✅ MATCH! {username} hash matches known hash")
                return username, hash_val

    # Thử với variations
    print("\nTrying username variations:")
    variations = []
    for base in ["ybtest"]:
        variations.extend([
            base.upper(),
            base.lower(),
            base.capitalize(),
            base + "123",
            "user_" + base,
            base + "_user",
        ])

    for username in variations:
        md5_hash = hashlib.md5(username.encode()).hexdigest().upper()
        if md5_hash in known_hashes:
            print(f"   ✅ FOUND: '{username}' -> MD5: {md5_hash}")
            return username, md5_hash

    print("   ❌ No direct hash matches found")
    return None, None


def main():
    print("=" * 60)
    print("🔓 GIẢI MÃ PACKET OPCODE 1")
    print("=" * 60)

    # Kiểm tra username hash trước
    username_result, hash_result = check_username_hashes()

    # Parse packet
    payload = parse_packet(PACKET_HEX)

    # Prepare key
    aes_key = binascii.unhexlify(AES_KEY_HEX)
    print(f"\n🔑 AES Key: {AES_KEY_HEX}")
    print(f"   Key length: {len(aes_key)} bytes")

    if len(payload) % 16 != 0:
        print(f"⚠️  Payload length ({len(payload)}) không chia hết cho 16 - có thể không phải AES")

    # Tách 16 bytes đầu (username) và phần còn lại
    username_encrypted = payload[:16]
    rest_payload = payload[16:]

    # Parse session ID từ packet header
    packet_data = binascii.unhexlify(PACKET_HEX)
    session_id = int.from_bytes(packet_data[4:8], 'little')

    print(f"\n📊 PHÂN TÍCH CẤU TRÚC:")
    print(f"   Session ID: {session_id}")
    print(f"   Username (16 bytes): {username_encrypted.hex()}")
    print(f"   Rest payload ({len(rest_payload)} bytes): {rest_payload.hex()[:64]}...")

    # Thử giải mã username riêng
    try_decrypt_username(aes_key, username_encrypted, session_id)

    print(f"\n🎯 GIẢI MÃ PHẦN CÒN LẠI:")

    valid_results = []

    # Thử giải mã phần còn lại với AES-256-CBC
    cbc_raw, cbc_unpadded = try_aes_cbc(aes_key, rest_payload)
    if cbc_raw:
        if analyze_plaintext(cbc_raw, "Rest payload - AES-256-CBC IV=0 (raw)"):
            valid_results.append(("AES-256-CBC IV=0 (raw)", cbc_raw))
        if cbc_unpadded != cbc_raw and analyze_plaintext(cbc_unpadded, "Rest payload - AES-256-CBC IV=0 (unpadded)"):
            valid_results.append(("AES-256-CBC IV=0 (unpadded)", cbc_unpadded))

    # Kết luận
    print(f"\n" + "=" * 60)
    print("📋 KẾT QUẢ:")
    print("✅ Cấu trúc packet login:")
    print("   - 16 bytes đầu: Username (mã hóa riêng)")
    print("   - 112 bytes sau: Data khác (AES-256-CBC, IV=0)")
    print("   - Chứa IP client: ***********")
    print("=" * 60)


if __name__ == '__main__':
    main()
