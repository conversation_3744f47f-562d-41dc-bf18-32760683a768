#!/usr/bin/env python3
import socket
import struct
import json
import time
import os
import sys
import signal
import argparse
import sqlite3
import threading
from datetime import datetime
from scapy.all import sniff, IP, TCP, Raw, get_if_list

# Import các module cần thiết từ packer_linux.py
try:
    from decompress import decompress_packet
    from encrypt import decrypt_data
    from utils.map_decode import save_map_data, save_shop_data
    from utils.util_fn import manage_login_packet, manage_packet
    has_utils = True
except ImportError:
    has_utils = False
    print("Cảnh báo: Không thể import các module tiện ích. Một số chức năng sẽ bị hạn chế.")

# Biến lưu trữ thông tin về packet gần nhất
last_packet = {
    'login': None,  # Lưu packet login gần nhất
    'normal': None,  # Lưu packet thông thường gần nhất
    'sub_packet': None  # Lưu sub_packet gần nhất
}

# Hàm so sánh packet hiện tại với packet gần nhất
def is_duplicate_packet(packet_type, current_packet, request_type=None, wordid=None):
    """
    Kiểm tra xem packet hiện tại có giống với packet gần nhất không

    Args:
        packet_type (str): Loại packet ('login', 'normal', 'sub_packet')
        current_packet (bytes): Nội dung packet hiện tại
        request_type (int, optional): Loại request của packet
        wordid (int, optional): WorldID của packet

    Returns:
        bool: True nếu packet trùng lặp, False nếu không
    """
    global last_packet

    if last_packet[packet_type] is None:
        # Nếu chưa có packet gần nhất, lưu packet hiện tại và trả về False
        last_packet[packet_type] = {
            'data': current_packet,
            'request_type': request_type,
            'wordid': wordid
        }
        return False

    # So sánh nội dung packet
    if last_packet[packet_type]['data'] == current_packet:
        # Nếu có request_type và wordid, so sánh thêm các thông tin này
        if request_type is not None and wordid is not None:
            if (last_packet[packet_type]['request_type'] == request_type and
                last_packet[packet_type]['wordid'] == wordid):
                return True
        else:
            return True

    # Nếu không trùng lặp, cập nhật packet gần nhất
    last_packet[packet_type] = {
        'data': current_packet,
        'request_type': request_type,
        'wordid': wordid
    }
    return False

class PacketCapture:
    def __init__(self, output_file=None, socket_path=None, interface=None, target_ip=None,
                 target_port=None, login_port=None, is_local=False, is_encrypted=False):
        self.output_file = output_file
        self.socket_path = socket_path
        self.interface = interface
        self.target_ip = target_ip
        self.target_port = target_port
        self.login_port = login_port
        self.is_local = is_local
        self.is_encrypted = is_encrypted
        self.running = False
        self.client_socket = None
        self.buffer = b""

        # Khởi tạo socket IPC nếu được chỉ định
        if self.socket_path:
            if os.path.exists(self.socket_path):
                os.unlink(self.socket_path)
            self.server_socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            self.server_socket.bind(self.socket_path)
            self.server_socket.listen(1)
            self.server_socket.settimeout(0.1)  # Non-blocking socket

    def accept_gui_connection(self):
        if self.socket_path:
            try:
                self.client_socket, _ = self.server_socket.accept()
                print("GUI đã kết nối")
            except socket.timeout:
                pass

    def send_to_gui(self, data):
        if self.client_socket:
            try:
                self.client_socket.sendall((json.dumps(data) + '\n').encode('utf-8'))
            except:
                self.client_socket = None

    def save_to_file(self, data):
        if self.output_file:
            with open(self.output_file, 'a') as f:
                f.write(json.dumps(data) + '\n')

    def process_packet(self, packet):
        if packet.haslayer(IP) and packet.haslayer(TCP) and packet.haslayer(Raw):
            ip_layer = packet.getlayer(IP)
            tcp_layer = packet.getlayer(TCP)
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            print(f"DEBUG: Packet detected - {ip_layer.src}:{tcp_layer.sport} -> {ip_layer.dst}:{tcp_layer.dport}")
            print(f"DEBUG: Target port: {self.target_port}, Login port: {self.login_port}")

            # Xử lý gói tin login
            if self.login_port and (tcp_layer.dport == self.login_port or tcp_layer.sport == self.login_port):
                print(f"LOGIN PACKET DETECTED - {tcp_layer.dport} {tcp_layer.sport}")
                direction = ">>>" if tcp_layer.dport == self.login_port else "<"
                raw_data = packet[Raw].load
                sub_packet = bytes(raw_data)

                # Kiểm tra trùng lặp
                if is_duplicate_packet('login', sub_packet):
                    print(f"Bỏ qua login packet trùng lặp")
                    return

                # Xử lý login packet
                if has_utils:
                    request_type, wordid = manage_login_packet(sub_packet)
                else:
                    request_type, wordid = None, None

                # Gửi dữ liệu đến GUI
                processed_data = {
                    'timestamp': timestamp,
                    'source_ip': ip_layer.src,
                    'source_port': tcp_layer.sport,
                    'dest_ip': ip_layer.dst,
                    'dest_port': tcp_layer.dport,
                    'protocol': 'TCP',
                    'request_type': request_type,
                    'wordid': wordid,
                    'direction': direction,
                    'data': sub_packet.hex()
                }
                self.send_to_gui(processed_data)
                self.save_to_file(processed_data)
                return

            # Xử lý gói tin thông thường (QUAN TRỌNG: Không kiểm tra port như login packet)
            # Logic này khớp với packer_linux.py gốc - chỉ xử lý direction, không filter theo port
            direction = ">>>" if tcp_layer.dport == self.target_port else "<"
            raw_data = packet[Raw].load
            data_bytes = bytes(raw_data)

            print(f"TARGET PACKET DETECTED - {tcp_layer.dport} {tcp_layer.sport} (direction: {direction})")

            # Kiểm tra trùng lặp
            if is_duplicate_packet('normal', data_bytes):
                print(f"Bỏ qua packet thông thường trùng lặp")
                return

            # In ra console các packet không bắt đầu bằng AA55 hoặc không kết thúc bằng 55AA
            if not data_bytes.startswith(b'\xAA\x55') or not data_bytes.endswith(b'\x55\xAA'):
                print(f"Packet không chuẩn - IP:{ip_layer.src}:{tcp_layer.sport} -> {ip_layer.dst}:{tcp_layer.dport}")
                print(f"  - Data length: {len(data_bytes)} bytes")
                print(f"  - Hex: {data_bytes.hex()}")
                if len(data_bytes) > 0:
                    print(f"  - Bắt đầu: {data_bytes[:2].hex()}")
                    print(f"  - Kết thúc: {data_bytes[-2:].hex()}")
                print("----------")

            self.buffer += data_bytes

            while True:
                end_index = self.buffer.find(b'\x55\xAA')
                if end_index == -1:
                    break
                start_index = self.buffer.find(b'\xAA\x55')
                if start_index == -1 or start_index > end_index:
                    break

                sub_packet = self.buffer[start_index:end_index+2]
                self.buffer = self.buffer[end_index+2:]

                # Giải mã nếu cần
                if self.is_encrypted and len(sub_packet) >= 16 and has_utils:
                    sub_packet = decrypt_data(sub_packet)

                # Xử lý gói tin
                if has_utils:
                    request_type, wordid = manage_packet(sub_packet)
                else:
                    request_type, wordid = None, None

                # Kiểm tra trùng lặp
                if is_duplicate_packet('sub_packet', sub_packet, request_type, wordid):
                    print(f"Bỏ qua sub-packet trùng lặp: {request_type}")
                    continue

                # Xử lý các loại gói tin đặc biệt
                if has_utils:
                    if request_type == 103:
                        save_map_data(sub_packet, 101)
                    if request_type == 642:
                        save_shop_data(sub_packet)

                # Gửi dữ liệu đến GUI
                processed_data = {
                    'timestamp': timestamp,
                    'source_ip': ip_layer.src,
                    'source_port': tcp_layer.sport,
                    'dest_ip': ip_layer.dst,
                    'dest_port': tcp_layer.dport,
                    'protocol': 'TCP',
                    'request_type': request_type,
                    'wordid': wordid,
                    'direction': direction,
                    'data': sub_packet.hex()
                }
                self.send_to_gui(processed_data)
                self.save_to_file(processed_data)

            # Xử lý buffer còn lại
            if len(self.buffer) > 0 and self.buffer[-2:] != b'\x55\xAA':
                incomplete_start = self.buffer.find(b'\xAA\x55')
                if incomplete_start != -1:
                    self.buffer = self.buffer[incomplete_start:]
                else:
                    self.buffer = b""

    def start_capture(self):
        if not self.interface:
            print("Lỗi: Không có giao diện mạng được chỉ định")
            return

        if not self.target_ip:
            print("Lỗi: Không có địa chỉ IP đích được chỉ định")
            return

        self.running = True
        print(f"Bắt đầu bắt gói tin trên {self.interface} cho {self.target_ip}...")

        # Tạo bộ lọc BPF
        if self.is_local:
            if self.login_port and self.target_port:
                bpf_filter = f"host {self.target_ip} and (port {self.target_port} or port {self.login_port})"
            elif self.target_port:
                bpf_filter = f"host {self.target_ip} and port {self.target_port}"
            else:
                bpf_filter = f"host {self.target_ip}"
        else:
            bpf_filter = f"host {self.target_ip}"

        try:
            # Bắt đầu thread chấp nhận kết nối từ GUI
            if self.socket_path:
                accept_thread = threading.Thread(target=self.accept_connections)
                accept_thread.daemon = True
                accept_thread.start()

            # Bắt gói tin
            sniff(filter=bpf_filter,
                  iface=self.interface,
                  prn=self.process_packet,
                  store=0,
                  stop_filter=lambda _: not self.running)

        except KeyboardInterrupt:
            print("Đã dừng bắt gói tin")
        except Exception as e:
            print(f"Lỗi khi bắt gói tin: {e}")
        finally:
            self.cleanup()

    def accept_connections(self):
        while self.running:
            if not self.client_socket:
                self.accept_gui_connection()
            time.sleep(0.1)

    def cleanup(self):
        self.running = False
        if self.client_socket:
            self.client_socket.close()
        if hasattr(self, 'server_socket'):
            self.server_socket.close()
        if self.socket_path and os.path.exists(self.socket_path):
            os.unlink(self.socket_path)

def signal_handler(sig, frame):
    print("Đã nhận tín hiệu dừng, đang thoát...")
    if 'packet_capture' in globals():
        packet_capture.cleanup()
    sys.exit(0)

def get_available_interfaces():
    try:
        return get_if_list()
    except Exception as e:
        print(f"Lỗi khi lấy danh sách giao diện mạng: {e}")
        return []

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Bắt gói tin mạng')
    parser.add_argument('-o', '--output', help='File để lưu dữ liệu gói tin')
    parser.add_argument('-s', '--socket', default='/tmp/packet_capture.sock',
                        help='Đường dẫn đến socket Unix để giao tiếp với GUI')
    parser.add_argument('-i', '--interface', help='Giao diện mạng để bắt gói tin')
    parser.add_argument('-t', '--target', help='Địa chỉ IP đích')
    parser.add_argument('-p', '--port', type=int, help='Cổng đích')
    parser.add_argument('-l', '--login-port', type=int, help='Cổng đăng nhập')
    parser.add_argument('--local', action='store_true', help='Bắt gói tin cục bộ')
    parser.add_argument('--encrypted', action='store_true', help='Gói tin được mã hóa')

    args = parser.parse_args()

    # Kiểm tra quyền root
    # if os.geteuid() != 0:
    #     print("Lỗi: Cần quyền root để bắt gói tin. Hãy chạy với sudo.")
    #     sys.exit(1)

    # Nếu không có giao diện mạng được chỉ định, hiển thị danh sách và thoát
    if not args.interface:
        interfaces = get_available_interfaces()
        if interfaces:
            print("Các giao diện mạng có sẵn:")
            for i, iface in enumerate(interfaces):
                print(f"  {i+1}. {iface}")
            print("\nHãy chỉ định giao diện mạng với tham số -i/--interface")
        else:
            print("Không tìm thấy giao diện mạng nào")
        sys.exit(1)

    # Nếu không có địa chỉ IP đích, hiển thị thông báo và thoát
    if not args.target:
        print("Lỗi: Cần chỉ định địa chỉ IP đích với tham số -t/--target")
        sys.exit(1)

    # Đăng ký xử lý tín hiệu
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Tạo và chạy packet capture
    packet_capture = PacketCapture(
        output_file=args.output,
        socket_path=args.socket,
        interface=args.interface,
        target_ip=args.target,
        target_port=args.port,
        login_port=args.login_port,
        is_local=args.local,
        is_encrypted=args.encrypted
    )
    packet_capture.start_capture()