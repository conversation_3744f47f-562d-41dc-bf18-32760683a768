from .util_fn import read1, read2, read<PERSON><PERSON><PERSON>, readfloat, split_packets

def handle_character_info_17(buffer):
    characters = split_packets(buffer);
    if (len(characters) == 0):
         print("No Character found")
         return
    print(f"Found {len(characters)} characters");
    character_infos = []

    for character in characters:
        buffer = bytes.fromhex(character)
        offset = 12
        fld_index = read1(buffer,offset)
        name = read<PERSON><PERSON><PERSON>(buffer,offset+1,14) #start from 11
        offset= 49+2
        zx = read2(buffer,offset)
        level = read2(buffer,offset+2)
        job_level = read2(buffer,offset+4)
        job = read1(buffer,offset+6)
        mautoc = read2(buffer,offset+8)
        kieutoc = read2(buffer,offset+10)
        offset= 70+2
        gioitinh = read1(buffer,offset)
        x = readfloat(buffer,offset+1)
        z = readfloat(buffer,offset+5)
        y = readfloat(buffer,offset+9)
        menow = read2(buffer,offset+13)
        offset= 167+2
        hp = read2(buffer,offset)
        mp = read2(buffer,offset+2)

        character_data = {
            "index": fld_index,
            "name" : name,
            "zx" : zx,
            "level" : level,
            "job_level" : job_level,
            "job" : job,
            "mautoc" : mautoc,
            "kieutoc" : kieutoc,
            "gioitinh" : gioitinh,
            "x" : x,
            "z" : z,
            "y" : y,
            "menow" : menow,
            "hp" : hp,
            "mp" : mp
        }

        character_infos.append(character_data)

    return character_infos
