import colorsys
import tkinter as tk
from PIL import Image, ImageTk
import sqlite3
from collections import Counter
from prettytable import PrettyTable

IMAGE_SIZE = 512

def hsl_to_rgb(h, s, l):
    # Convert HSL to RGB values
    return tuple(round(c * 255) for c in colorsys.hls_to_rgb(h, l, s))

def fetch_monster_positions(map):
    try:
        conn = sqlite3.connect("npc_data.db")
        cursor = conn.cursor()

        # Query to fetch fld_x, fld_y, and fld_pid
        cursor.execute(f"SELECT fld_x2, fld_y2, fld_pid FROM npc_data where wordid = {map} order by fld_pid")
        monster_positions = cursor.fetchall()

        conn.close()
        return monster_positions
    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        return []

def group_monsters_by_fld_pid(monster_positions):
    counter = Counter(fld_pid for _, _, fld_pid in monster_positions)
    return counter.items()

def display_table(results, table_frame):
    # Clear the previous table content
    for widget in table_frame.winfo_children():
        widget.destroy()

    table = PrettyTable()
    table.field_names = ["fld_pid", "count"]
    total_monsters = 0

    for fld_pid, count in results:
        table.add_row([fld_pid, count])
        total_monsters += count

    table.add_row(["Total Monsters", total_monsters])
    table_widget = tk.Label(table_frame, text=str(table), font=("Courier", 10), justify=tk.LEFT)
    table_widget.pack()

def update_canvas(canvas, image_path, monster_positions, fld_map):
    try:
        # Clear previous drawings on canvas
        canvas.delete("all")

        # Load image from IMAGE folder
        image = Image.open(image_path)
        image.thumbnail((IMAGE_SIZE, IMAGE_SIZE), Image.LANCZOS)

        # Convert PIL Image to PhotoImage
        tk_image = ImageTk.PhotoImage(image)

        # Display image as background of the canvas
        canvas.create_image(IMAGE_SIZE // 2, IMAGE_SIZE // 2, image=tk_image)

        # Draw each monster as a dot on the canvas
        for position in monster_positions:
            fld_x2, fld_y2, fld_id = position
            mapsize = 5120
            hue = (fld_id * 0.618033988749895) % 1.0  # Golden ratio hue shift
            saturation = 0.8
            lightness = 0.5
            r, g, b = hsl_to_rgb(hue, saturation, lightness)
            color_hex = f'#{r:02x}{g:02x}{b:02x}'

            offsetX = mapsize / 2 - mapsize if fld_map == 201 else mapsize / 2 + mapsize if fld_map == 301 else mapsize / 2
            offsetY = mapsize / 2
            scale = 512 / mapsize

            # Calculate canvas coordinates from fld_x, fld_y
            canvas_x = (fld_x2 + offsetX) * scale
            canvas_y = 512 - ((fld_y2 + offsetY) * scale)

            # Draw a dot (circle) for the monster
            dot_radius = 3
            canvas.create_oval(canvas_x - dot_radius, canvas_y - dot_radius,
                               canvas_x + dot_radius, canvas_y + dot_radius,
                               fill=color_hex, outline='black')

        # Keep a reference to the image to prevent garbage collection
        canvas.image = tk_image

    except FileNotFoundError:
        print(f"Image {image_path} not found.")
    except Exception as e:
        print(f"Error updating canvas: {e}")

def show_map_image(fld_map):
    try:
        # Create a new window for displaying the image
        image_window = tk.Toplevel()
        image_window.title(f"Map {fld_map}")

        # Create a canvas widget on top of the image window
        canvas = tk.Canvas(image_window, width=IMAGE_SIZE, height=IMAGE_SIZE, bg='white')
        canvas.pack(side=tk.LEFT)

        # Create a frame for displaying the table
        table_frame = tk.Frame(image_window)
        table_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Function to update the canvas with monster positions
        def update_canvas_and_table():
            monster_positions = fetch_monster_positions(fld_map)
            image_path = f"IMAGE/{fld_map}.jpg"
            update_canvas(canvas, image_path, monster_positions, fld_map)
            fld_pid_counts = group_monsters_by_fld_pid(monster_positions)
            display_table(fld_pid_counts, table_frame)
            image_window.after(1000, update_canvas_and_table)

        update_canvas_and_table()

        # Start the Tkinter main loop for the image window
        image_window.mainloop()

    except Exception as e:
        print(f"Error displaying map image: {e}")

# Example usage
""" root = tk.Tk()
root.title("Map Display")

btn_map_201 = tk.Button(root, text="Show Map 201", command=lambda: show_map_image(201))
btn_map_201.pack()

btn_map_301 = tk.Button(root, text="Show Map 301", command=lambda: show_map_image(301))
btn_map_301.pack()

root.mainloop() """
