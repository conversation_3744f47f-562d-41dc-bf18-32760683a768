#!/bin/bash

# Ki<PERSON><PERSON> tra xem script có được chạy với quyền root không
if [ "$EUID" -ne 0 ]; then
    echo "Vui lòng chạy script này với quyền root (sudo)"
    exit 1
fi

# Đường dẫn đến socket
SOCKET_PATH="/tmp/packet_capture.sock"

# Xóa socket cũ nếu tồn tại
if [ -e "$SOCKET_PATH" ]; then
    rm "$SOCKET_PATH"
fi

# Lấy danh sách giao diện mạng
echo "Các giao diện mạng có sẵn:"
python3 packet_capture.py -i help 2>/dev/null || echo "Không thể lấy danh sách giao diện mạng"

# Yêu cầu người dùng nhập thông tin
echo ""
read -p "Nhập giao diện mạng (ví dụ: eth0, wlan0): " INTERFACE
read -p "Nhập địa chỉ IP đích: " TARGET_IP
read -p "Nhập cổng đích (target port): " TARGET_PORT
read -p "Nhập cổng đăng nhập (login port, để trống nếu không có): " LOGIN_PORT
read -p "Bắt gói tin cục bộ? (y/n): " IS_LOCAL
read -p "Gói tin có được mã hóa? (y/n): " IS_ENCRYPTED

# Tạo tham số cho packet_capture
CAPTURE_ARGS="-i $INTERFACE -t $TARGET_IP -s $SOCKET_PATH"

if [ ! -z "$TARGET_PORT" ]; then
    CAPTURE_ARGS="$CAPTURE_ARGS -p $TARGET_PORT"
fi

if [ ! -z "$LOGIN_PORT" ]; then
    CAPTURE_ARGS="$CAPTURE_ARGS -l $LOGIN_PORT"
fi

if [ "$IS_LOCAL" = "y" ] || [ "$IS_LOCAL" = "Y" ]; then
    CAPTURE_ARGS="$CAPTURE_ARGS --local"
fi

if [ "$IS_ENCRYPTED" = "y" ] || [ "$IS_ENCRYPTED" = "Y" ]; then
    CAPTURE_ARGS="$CAPTURE_ARGS --encrypted"
fi

echo "Khởi động packet capture với tham số: $CAPTURE_ARGS"

# Khởi động quá trình bắt gói tin trong nền
python3 packet_capture.py $CAPTURE_ARGS &
CAPTURE_PID=$!

# Đợi socket được tạo
echo "Đang chờ socket được tạo..."
TIMEOUT=10
COUNTER=0
while [ ! -e "$SOCKET_PATH" ] && [ $COUNTER -lt $TIMEOUT ]; do
    sleep 0.5
    COUNTER=$((COUNTER + 1))
done

if [ ! -e "$SOCKET_PATH" ]; then
    echo "Lỗi: Socket không được tạo trong thời gian chờ"
    kill $CAPTURE_PID 2>/dev/null
    exit 1
fi

echo "Socket đã được tạo, khởi động GUI..."

# Thiết lập biến môi trường cho Hyprland/Wayland
export WAYLAND_DISPLAY=$WAYLAND_DISPLAY
export XDG_RUNTIME_DIR=$XDG_RUNTIME_DIR
export DISPLAY=$DISPLAY

# Khởi động giao diện với quyền người dùng thường
sudo -u $SUDO_USER -E WAYLAND_DISPLAY=$WAYLAND_DISPLAY XDG_RUNTIME_DIR=$XDG_RUNTIME_DIR DISPLAY=$DISPLAY python3 packet_gui.py -s "$SOCKET_PATH"

# Khi giao diện đóng, dừng quá trình bắt gói tin
echo "Đang dừng packet capture..."
kill $CAPTURE_PID 2>/dev/null

# Xóa socket
if [ -e "$SOCKET_PATH" ]; then
    rm "$SOCKET_PATH"
fi

echo "Hoàn thành!"