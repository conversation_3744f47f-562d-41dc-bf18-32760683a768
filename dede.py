#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import binascii

# Dữ liệu hex từ client và server
HEX1 = (
    "3082010a0282010100bbe03272f6cc958ab21c1c265c269aa4ade5b1635625dc89378f70db76a6c248de"
    "22223088032f47e479d4fbefcd1f48725bfd429456a3250311892c008cc39e2432b8409b212935a8a1cb"
    "599feb46ecdbd165d3ec19129019c4ffb819a022a4af1e7722ec2ae2e7d3216e4adaf7974459c22f4793"
    "262d4e973f89f41f1f145024088fe4906ee48c8773897ef8945e1425f43d8e803138d5334dedbdc33645"
    "49c2061e9e024e3ea8c6418bb3b29637d50170583ab2541192ca45bbc57cc94a0c06299ff10d0c5b9889"
    "9fb5eed3c660b2b2d8e981a13e548a8bbe8d029ea4e9aa82ce5164c9c1fb96ee67ba3a91b3d12c50200c"
    "62b9c0a438ce06706c9f1fbb390203010001000e0100000000000000000000000000000000000001000100"
)

HEX2 = (
    "5980cdae67b4d618b18b80101bca9836ef4fd52e1784c2bf8667d745d5f4ad50ca838a23eabfaaf6212e"
    "6b0acd28e8822bd8ceff49641cae9ef75eefefc77751429d64fb268c914598532ae0b1e30bd3b82fd791"
    "7eca85fb0f30687b32641a1ca62d78d411b1ef49327852ec6b09034833bc206a9e76ac32b03cc9a2849b"
    "93410c4653488ec275c2b40b9a73d46de34d8c2400fc8e59b4b37d4252791977fd05f4e83b4a7cc20c45"
    "8eaac923781ca7ceef1deff405b121ec1e289c50e6e78845081ba7c21101294c0a9a58f94a464b8a96c0"
    "0e2873a2c4cd499d2e048f8fb6914bb7af035a6577598fca1b6a2a8c331075940ad8088734ecd3f185a8"
    "ee8dce260000000000000000000000000000000001000068e0e562ad856ee867f8d0fd15f11f7302000100"
)


def read_asn1_len(buf, off):
    """Đọc độ dài ASN.1 DER"""
    if off >= len(buf):
        raise ValueError("Out of bounds length read")
    first = buf[off]
    off += 1
    if first < 0x80:
        return first, off
    num_octets = first & 0x7F
    if num_octets == 0 or num_octets > 4:
        raise ValueError("Unsupported DER length form")
    if off + num_octets > len(buf):
        raise ValueError("Out of bounds in length")
    val = 0
    for i in range(num_octets):
        val = (val << 8) | buf[off + i]
    off += num_octets
    return val, off


def parse_rsa_public_key_der(der: bytes):
    """Parse RSA public key từ DER format"""
    off = 0
    if der[off] != 0x30:
        raise ValueError("Not a DER SEQUENCE for RSAPublicKey")
    seqlen, off = read_asn1_len(der, off + 1)
    seq_end = off + seqlen

    # Parse modulus (n)
    if der[off] != 0x02:
        raise ValueError("Expected INTEGER (modulus)")
    nlen, off = read_asn1_len(der, off + 1)
    n_bytes = der[off:off + nlen]
    off += nlen
    # Strip leading 0x00 if present (positive sign)
    if len(n_bytes) > 0 and n_bytes[0] == 0x00:
        n_bytes_stripped = n_bytes[1:]
    else:
        n_bytes_stripped = n_bytes
    n = int.from_bytes(n_bytes_stripped, 'big')

    # Parse exponent (e)
    if der[off] != 0x02:
        raise ValueError("Expected INTEGER (exponent)")
    elen, off = read_asn1_len(der, off + 1)
    e_bytes = der[off:off + elen]
    off += elen
    e = int.from_bytes(e_bytes, 'big')

    return n, e, seq_end


# Known DigestInfo ASN.1 prefixes for PKCS#1 v1.5
DIGESTINFO_PREFIXES = {
    'sha1': binascii.unhexlify('3021300906052b0e03021a05000414'),
    'sha256': binascii.unhexlify('3031300d060960864801650304020105000420'),
    'sha384': binascii.unhexlify('3041300d060960864801650304020205000430'),
    'sha512': binascii.unhexlify('3051300d060960864801650304020305000440'),
    'md5': binascii.unhexlify('3020300c06082a864886f70d020505000410'),
}


def analyze_signature_or_ciphertext(rsa_block, n, e):
    """Phân tích xem RSA block là signature hay ciphertext"""
    k = (n.bit_length() + 7) // 8
    s_int = int.from_bytes(rsa_block, 'big')

    # Thực hiện RSA public operation: s^e mod n
    em_int = pow(s_int, e, n)
    em = em_int.to_bytes(k, 'big')

    print(f"  + RSA public operation result (first 32 bytes): {binascii.hexlify(em[:32]).decode()}")

    # Kiểm tra PKCS#1 v1.5 signature format: 00 01 FF...FF 00 DigestInfo
    if len(em) >= 11 and em[0] == 0x00 and em[1] == 0x01:
        # Tìm 0x00 sau dải FF
        i = 2
        while i < len(em) and em[i] == 0xFF:
            i += 1
        if i < len(em) and em[i] == 0x00:
            i += 1
            di = em[i:]
            print(f"  + Có cấu trúc PKCS#1 v1.5 padding (00 01 FF...00)")
            print(f"  + DigestInfo length: {len(di)} bytes")

            # Kiểm tra các DigestInfo OID phổ biến
            for name, prefix in DIGESTINFO_PREFIXES.items():
                if di.startswith(prefix):
                    expected_len = {
                        'sha1': 20, 'sha256': 32, 'sha384': 48,
                        'sha512': 64, 'md5': 16
                    }[name]
                    di_rest = di[len(prefix):]
                    if len(di_rest) == expected_len:
                        print(f"  + ✅ Đây là RSA signature với {name.upper()}")
                        print(f"  + Hash digest: {binascii.hexlify(di_rest).decode()}")
                        return "signature", name, di_rest
                    else:
                        print(f"  + ⚠️  Có prefix {name} nhưng digest length không khớp ({len(di_rest)} != {expected_len})")

            print(f"  + ❓ Có padding PKCS#1 v1.5 nhưng DigestInfo không khớp OID phổ biến")
            print(f"  + DigestInfo hex: {binascii.hexlify(di).decode()}")
            return "unknown_signature", None, di
        else:
            print(f"  + ❌ Không tìm thấy 0x00 sau dải FF trong PKCS#1 padding")
    else:
        print(f"  + ❌ Không có cấu trúc PKCS#1 v1.5 signature padding")
        print(f"  + Có thể là RSA ciphertext (PKCS#1 v1.5 encryption hoặc OAEP)")

    return "ciphertext", None, None


def main():
    print("=" * 60)
    print("🔍 PHÂN TÍCH DỮ LIỆU XÁC THỰC RSA")
    print("=" * 60)

    # Phân tích đoạn 1 (public key)
    print("\n📤 [ĐOẠN 1] Dữ liệu gửi xuống client:")
    print(f"Độ dài: {len(HEX1)//2} bytes")

    b1 = binascii.unhexlify(HEX1)

    try:
        n, e, der_len = parse_rsa_public_key_der(b1)
        k = (n.bit_length() + 7) // 8

        print(f"✅ Nhận diện: RSA Public Key (ASN.1 DER)")
        print(f"📏 Key size: {n.bit_length()} bits ({k} bytes)")
        print(f"🔢 Exponent (e): {e}")
        print(f"📦 DER structure length: {der_len} bytes")

        # Hiển thị modulus rút gọn
        n_hex = binascii.hexlify(n.to_bytes(k, 'big')).decode()
        print(f"🔑 Modulus (n): {n_hex[:32]}...{n_hex[-32:]}")

        # Kiểm tra bytes dư
        if der_len < len(b1):
            tail = b1[der_len:]
            print(f"📎 Có {len(tail)} bytes dư sau DER: {binascii.hexlify(tail).decode()}")

            # Phân tích bytes dư
            if len(tail) >= 4:
                print(f"   - 4 bytes đầu: {binascii.hexlify(tail[:4]).decode()}")
                print(f"   - 4 bytes cuối: {binascii.hexlify(tail[-4:]).decode()}")

    except Exception as ex:
        print(f"❌ Không parse được RSA public key: {ex}")
        return

    # Phân tích đoạn 2 (response data)
    print(f"\n📥 [ĐOẠN 2] Dữ liệu gửi lại:")
    print(f"Độ dài: {len(HEX2)//2} bytes")

    b2 = binascii.unhexlify(HEX2)

    if len(b2) >= k:
        rsa_block = b2[:k]
        tail = b2[k:]

        print(f"🔐 RSA block: {k} bytes")
        print(f"📎 Tail data: {len(tail)} bytes")

        # Phân tích RSA block
        result_type, hash_alg, digest = analyze_signature_or_ciphertext(rsa_block, n, e)

        # Phân tích tail data
        if tail:
            print(f"\n📎 Phân tích tail data ({len(tail)} bytes):")
            print(f"   Hex: {binascii.hexlify(tail).decode()}")

            # Tìm patterns trong tail
            zero_count = tail.count(0)
            if zero_count > len(tail) * 0.7:
                print(f"   - Chủ yếu là zero bytes ({zero_count}/{len(tail)})")

            # Tìm non-zero bytes cuối
            nonzero_tail = []
            for i in range(len(tail)-1, -1, -1):
                if tail[i] != 0:
                    nonzero_tail = tail[i-min(15,i):i+1]
                    break

            if nonzero_tail:
                print(f"   - Non-zero cuối: {binascii.hexlify(nonzero_tail).decode()}")

    else:
        print(f"❌ Độ dài đoạn 2 ({len(b2)} bytes) < RSA key size ({k} bytes)")

    print("\n" + "=" * 60)
    print("� KẾT LUẬN:")
    if 'result_type' in locals():
        if result_type == "signature":
            print(f"✅ Đây là hệ thống RSA digital signature với {hash_alg.upper()}")
            print("🔄 Quy trình: Client gửi public key → Server ký dữ liệu → Client verify")
        elif result_type == "unknown_signature":
            print("⚠️  Có thể là RSA signature nhưng dùng hash algorithm không phổ biến")
        else:
            print("❓ Có thể là RSA encryption hoặc key exchange")
    print("=" * 60)


if __name__ == '__main__':
    main()
