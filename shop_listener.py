import socket
import threading
import struct
import json
import os
from datetime import datetime
from encrypt import decrypt_data
from utils.util_fn import manage_packet

class ShopDataListener:
    def __init__(self, listen_ip="*************", listen_port=15000):
        self.listen_ip = listen_ip
        self.listen_port = listen_port
        self.buffer = b""
        self.shop_data_file = "shop_data.json"
        self.running = False
        self.socket = None
        
        # Khởi tạo file JSON nếu chưa tồn tại
        self.initialize_shop_data_file()
    
    def initialize_shop_data_file(self):
        """Khởi tạo file JSON để lưu shop data"""
        if not os.path.exists(self.shop_data_file):
            with open(self.shop_data_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, indent=2, ensure_ascii=False)
    
    def load_shop_data(self):
        """Load dữ liệu shop hiện tại từ file JSON"""
        try:
            with open(self.shop_data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {}
    
    def save_shop_data(self, shop_data):
        """Lưu dữ liệu shop vào file JSON"""
        with open(self.shop_data_file, 'w', encoding='utf-8') as f:
            json.dump(shop_data, f, indent=2, ensure_ascii=False)
    
    def parse_shop_packet(self, packet_data):
        """Parse packet shop data theo cấu trúc được mô tả"""
        try:
            # Kiểm tra opcode tại offset 0x10 (4 bytes sau header)
            if len(packet_data) < 0x18:  # Cần ít nhất đến offset 0x14 + 4 bytes
                return None
            
            # Parse 4 byte tiếp theo sau opcode để kiểm tra xem có phải là 3 không
            check_value = struct.unpack('<I', packet_data[0x10:0x14])[0]
            if check_value != 3:
                return None
            
            # Parse shopID tại offset 0x14
            shop_id = struct.unpack('<I', packet_data[0x14:0x18])[0]
            
            # Parse số lượng items tại offset 0x18
            item_count = struct.unpack('<I', packet_data[0x18:0x1C])[0]
            
            # Skip 4 bytes trống tại offset 0x1C
            # Parse tab/num value tại offset 0x20
            tab_value = struct.unpack('<Q', packet_data[0x20:0x28])[0]
            
            print(f"[SHOP DATA] ShopID: {shop_id}, Items: {item_count}, Tab: {tab_value}")
            
            # Parse từng item
            items = []
            offset = 0x28  # Bắt đầu từ sau tab_value
            
            for i in range(item_count):
                if offset + 8 > len(packet_data):
                    break
                
                # Parse FLD_PID (8 bytes)
                fld_pid = struct.unpack('<Q', packet_data[offset:offset+8])[0]
                offset += 8
                
                item_data = {
                    "position": i,
                    "fld_pid": fld_pid,
                    "magic_values": []
                }
                
                # Parse FLD_MAGICZh (8 bytes)
                if offset + 8 <= len(packet_data):
                    magic_zh = struct.unpack('<Q', packet_data[offset:offset+8])[0]
                    offset += 8
                    
                    if magic_zh > 0:
                        item_data["magic_zh"] = magic_zh
                        
                        # Parse các magic values (MAGIC1-4) nếu có
                        for magic_num in range(1, 5):
                            if offset + 8 <= len(packet_data):
                                magic_val = struct.unpack('<Q', packet_data[offset:offset+8])[0]
                                offset += 8
                                if magic_val > 0:
                                    item_data["magic_values"].append({
                                        f"magic_{magic_num}": magic_val
                                    })
                                else:
                                    break
                    else:
                        item_data["magic_zh"] = 0
                        # Skip 8 bytes cho giá trị 0
                        offset += 8
                
                # Parse -1L (8 bytes)
                if offset + 8 <= len(packet_data):
                    end_marker = struct.unpack('<q', packet_data[offset:offset+8])[0]  # signed
                    offset += 8
                    item_data["end_marker"] = end_marker
                
                items.append(item_data)
            
            return {
                "shop_id": shop_id,
                "item_count": item_count,
                "tab_value": tab_value,
                "items": items,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"[ERROR] Lỗi khi parse shop packet: {e}")
            return None
    
    def update_shop_data_file(self, parsed_data):
        """Cập nhật file JSON với dữ liệu shop mới"""
        if not parsed_data:
            return
        
        shop_data = self.load_shop_data()
        shop_id = str(parsed_data["shop_id"])
        
        # Kiểm tra xem shop đã tồn tại chưa
        if shop_id not in shop_data:
            shop_data[shop_id] = {
                "shop_id": parsed_data["shop_id"],
                "first_seen": parsed_data["timestamp"],
                "last_updated": parsed_data["timestamp"],
                "items": {}
            }
        else:
            shop_data[shop_id]["last_updated"] = parsed_data["timestamp"]
        
        # Cập nhật items theo vị trí
        for item in parsed_data["items"]:
            position = str(item["position"])
            shop_data[shop_id]["items"][position] = {
                "fld_pid": item["fld_pid"],
                "magic_zh": item.get("magic_zh", 0),
                "magic_values": item.get("magic_values", []),
                "end_marker": item.get("end_marker", -1),
                "last_updated": parsed_data["timestamp"]
            }
        
        self.save_shop_data(shop_data)
        print(f"[SAVED] Đã lưu shop data cho ShopID {shop_id} với {len(parsed_data['items'])} items")
    
    def process_packet_data(self, data):
        """Xử lý dữ liệu packet tương tự packet_callback"""
        self.buffer += data
        
        while True:
            # Tìm end marker
            end_index = self.buffer.find(b'\x55\xAA')
            if end_index == -1:
                break
            
            # Tìm start marker
            start_index = self.buffer.find(b'\xAA\x55')
            if start_index == -1 or start_index > end_index:
                # Nếu không tìm thấy start marker hoặc start marker sau end marker
                # Bỏ qua packet không đủ và tìm start marker tiếp theo
                next_start = self.buffer.find(b'\xAA\x55', end_index + 2)
                if next_start != -1:
                    self.buffer = self.buffer[next_start:]
                    continue
                else:
                    break
            
            # Trích xuất sub_packet
            sub_packet = self.buffer[start_index:end_index+2]
            self.buffer = self.buffer[end_index+2:]
            
            # Decrypt data nếu cần
            try:
                decrypted_packet = decrypt_data(sub_packet) if len(sub_packet) >= 16 else sub_packet
            except:
                decrypted_packet = sub_packet
            
            # Lấy opcode
            try:
                request_type, wordid = manage_packet(decrypted_packet)
                
                if request_type == 145:
                    print(f"[DETECTED] Packet với opcode 145 - WordID: {wordid}")
                    
                    # Parse và lưu shop data
                    parsed_shop_data = self.parse_shop_packet(decrypted_packet)
                    if parsed_shop_data:
                        self.update_shop_data_file(parsed_shop_data)
                    
            except Exception as e:
                print(f"[ERROR] Lỗi khi xử lý packet: {e}")
                continue
        
        # Xử lý buffer còn lại
        if len(self.buffer) > 0 and not self.buffer.endswith(b'\x55\xAA'):
            incomplete_start = self.buffer.find(b'\xAA\x55')
            if incomplete_start != -1:
                self.buffer = self.buffer[incomplete_start:]
            else:
                self.buffer = b""
    
    def handle_client(self, client_socket, address):
        """Xử lý kết nối từ client"""
        print(f"[CONNECTED] Client từ {address}")
        
        try:
            while self.running:
                data = client_socket.recv(4096)
                if not data:
                    break
                
                print(f"[RECEIVED] {len(data)} bytes từ {address}")
                self.process_packet_data(data)
                
        except Exception as e:
            print(f"[ERROR] Lỗi khi xử lý client {address}: {e}")
        finally:
            client_socket.close()
            print(f"[DISCONNECTED] Client {address}")
    
    def start_listening(self):
        """Bắt đầu lắng nghe kết nối"""
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            self.socket.bind((self.listen_ip, self.listen_port))
            self.socket.listen(5)
            self.running = True
            
            print(f"[LISTENING] Server đang lắng nghe tại {self.listen_ip}:{self.listen_port}")
            print("Nhấn Ctrl+C để dừng server...")
            
            while self.running:
                try:
                    client_socket, address = self.socket.accept()
                    client_thread = threading.Thread(
                        target=self.handle_client, 
                        args=(client_socket, address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error:
                    if self.running:
                        print("[ERROR] Lỗi khi accept connection")
                    break
                    
        except Exception as e:
            print(f"[ERROR] Lỗi khi khởi tạo server: {e}")
        finally:
            self.stop_listening()
    
    def stop_listening(self):
        """Dừng server"""
        self.running = False
        if self.socket:
            self.socket.close()
        print("[STOPPED] Server đã dừng")

def main():
    # Cấu hình IP và port để lắng nghe
    listen_ip = "*************"
    listen_port = 15000
    # listen_port = int(listen_port) if listen_port.isdigit() else 15000
    
    # Tạo và khởi chạy listener
    listener = ShopDataListener(listen_ip, listen_port)
    
    try:
        listener.start_listening()
    except KeyboardInterrupt:
        print("\n[STOPPING] Đang dừng server...")
        listener.stop_listening()

if __name__ == "__main__":
    main()
