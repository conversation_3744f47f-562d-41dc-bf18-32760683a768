import threading
import struct
import json
import os
from datetime import datetime
from scapy.all import sniff, IP, TCP, Raw
from encrypt import decrypt_data
from utils.util_fn import manage_packet

class ShopDataListener:
    def __init__(self, target_ip="*************", target_port=15000):
        self.target_ip = target_ip
        self.target_port = target_port
        self.buffer = b""
        self.shop_data_file = "shop_data.json"
        self.running = False
        self.sniff_thread = None

        # Khởi tạo file JSON nếu chưa tồn tại
        self.initialize_shop_data_file()
    
    def initialize_shop_data_file(self):
        """Khởi tạo file JSON để lưu shop data"""
        if not os.path.exists(self.shop_data_file):
            with open(self.shop_data_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, indent=2, ensure_ascii=False)
    
    def load_shop_data(self):
        """Load dữ liệu shop hiện tại từ file JSON"""
        try:
            with open(self.shop_data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {}
    
    def save_shop_data(self, shop_data):
        """Lưu dữ liệu shop vào file JSON"""
        with open(self.shop_data_file, 'w', encoding='utf-8') as f:
            json.dump(shop_data, f, indent=2, ensure_ascii=False)
    
    def parse_shop_packet(self, packet_data):
        """Parse packet shop data theo cấu trúc được mô tả"""
        try:
            # Kiểm tra opcode tại offset 0x10 (4 bytes sau header)
            if len(packet_data) < 0x18:  # Cần ít nhất đến offset 0x14 + 4 bytes
                return None
            
            # Parse 4 byte tiếp theo sau opcode để kiểm tra xem có phải là 3 không
            check_value = struct.unpack('<I', packet_data[0x10:0x14])[0]
            if check_value != 3:
                return None
            
            # Parse shopID tại offset 0x14
            shop_id = struct.unpack('<I', packet_data[0x14:0x18])[0]

            # Parse số lượng items tại offset 0x18
            item_count = struct.unpack('<I', packet_data[0x18:0x1C])[0]

            # Skip 4 bytes trống tại offset 0x1C
            # Parse tab/num value tại offset 0x20
            tab_value = struct.unpack('<Q', packet_data[0x20:0x28])[0]

            # Xác định tab thực tế dựa vào logic đúng:
            if tab_value >= 101:
                # Tab cụ thể: 101 = tab 0, 102 = tab 1, 103 = tab 2, ...
                tab_index = tab_value - 101
                is_initial_load = False
                print(f"[SHOP DATA] ShopID: {shop_id}, Items: {item_count}, Tab Index: {tab_index} (tab_value: {tab_value})")
            else:
                # Lần đầu mở shop: tab_value = số lượng tab có trong shop
                # Mặc định hiển thị tab đầu tiên (index 0)
                tab_index = 0
                total_tabs = tab_value
                is_initial_load = True
                print(f"[SHOP DATA] ShopID: {shop_id}, Items: {item_count}, Initial load - Total tabs: {total_tabs}, Showing tab index: {tab_index}")

            print(f"[DEBUG] Packet length: {len(packet_data)} bytes")
            print(f"[DEBUG] Hex data: {packet_data.hex()[:200]}...")

            # Parse từng item
            items = []
            offset = 0x28  # Bắt đầu từ sau tab_value

            for i in range(item_count):
                if offset + 8 > len(packet_data):
                    break

                # Parse FLD_PID (8 bytes)
                fld_pid = struct.unpack('<Q', packet_data[offset:offset+8])[0]
                offset += 8

                # Slot position trong tab (0-59)
                slot_position = i

                # Khởi tạo magic values với 4 giá trị 0
                magic_values = [0, 0, 0, 0]

                # Parse FLD_MAGICZh (8 bytes) - số lượng magic items
                if offset + 8 <= len(packet_data):
                    magic_zh = struct.unpack('<Q', packet_data[offset:offset+8])[0]
                    offset += 8

                    # Parse các magic values (MAGIC1-4) nếu có
                    for magic_num in range(min(int(magic_zh), 4)):  # Tối đa 4 magic values
                        if offset + 8 <= len(packet_data):
                            magic_val = struct.unpack('<Q', packet_data[offset:offset+8])[0]
                            offset += 8
                            if magic_val > 0:
                                magic_values[magic_num] = magic_val

                # Skip end marker (8 bytes) - không lưu trữ
                if offset + 8 <= len(packet_data):
                    offset += 8

                item_data = {
                    "slot": slot_position,
                    "tab_index": tab_index,
                    "tab_value": tab_value,
                    "fld_pid": fld_pid,
                    "magic_zh": magic_zh if 'magic_zh' in locals() else 0,
                    "magic_values": magic_values
                }

                items.append(item_data)

            return {
                "shop_id": shop_id,
                "item_count": item_count,
                "tab_index": tab_index,
                "tab_value": tab_value,
                "is_initial_load": is_initial_load,
                "total_tabs": total_tabs if is_initial_load else None,
                "items": items,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"[ERROR] Lỗi khi parse shop packet: {e}")
            return None
    
    def update_shop_data_file(self, parsed_data):
        """Cập nhật file JSON với dữ liệu shop mới"""
        if not parsed_data:
            return

        shop_data = self.load_shop_data()
        shop_id = str(parsed_data["shop_id"])
        tab_index = parsed_data["tab_index"]
        tab_value = parsed_data["tab_value"]
        is_initial_load = parsed_data["is_initial_load"]

        # Khởi tạo shop nếu chưa tồn tại
        if shop_id not in shop_data:
            shop_data[shop_id] = {
                "shop_id": parsed_data["shop_id"],
                "tabs": {},
                "last_updated": parsed_data["timestamp"]
            }

        # Cập nhật thông tin shop
        shop_data[shop_id]["last_updated"] = parsed_data["timestamp"]

        # Nếu là lần đầu load, cập nhật tổng số tab
        if is_initial_load and parsed_data["total_tabs"]:
            shop_data[shop_id]["total_tabs"] = parsed_data["total_tabs"]

        # Sử dụng tab_index làm key (0, 1, 2, ...)
        tab_key = str(tab_index)

        # Khởi tạo tab nếu chưa tồn tại
        if tab_key not in shop_data[shop_id]["tabs"]:
            shop_data[shop_id]["tabs"][tab_key] = {
                "tab_index": tab_index,
                "tab_value": tab_value,
                "item_count": 0,
                "items": {},
                "last_updated": parsed_data["timestamp"]
            }

        # Cập nhật tab_value (có thể thay đổi giữa initial load và specific tab)
        shop_data[shop_id]["tabs"][tab_key]["tab_value"] = tab_value
        shop_data[shop_id]["tabs"][tab_key]["last_updated"] = parsed_data["timestamp"]

        # Cập nhật items theo slot trong tab (0-59)
        for item in parsed_data["items"]:
            slot = str(item["slot"])
            shop_data[shop_id]["tabs"][tab_key]["items"][slot] = {
                "slot": item["slot"],
                "tab_index": tab_index,
                "fld_pid": item["fld_pid"],
                "magic_zh": item["magic_zh"],
                "magic_values": item["magic_values"],
                "last_updated": parsed_data["timestamp"]
            }

        # Cập nhật item count cho tab
        shop_data[shop_id]["tabs"][tab_key]["item_count"] = len(parsed_data["items"])

        self.save_shop_data(shop_data)

        if is_initial_load:
            print(f"[SAVED] Đã lưu shop data cho ShopID {shop_id}, Tab {tab_index} (Initial load, total tabs: {parsed_data['total_tabs']}) với {len(parsed_data['items'])} items")
        else:
            print(f"[SAVED] Đã lưu shop data cho ShopID {shop_id}, Tab {tab_index} (tab_value: {tab_value}) với {len(parsed_data['items'])} items")
    
    def packet_callback(self, packet):
        """Callback function để xử lý packet từ scapy"""
        if packet.haslayer(IP) and packet.haslayer(TCP) and packet.haslayer(Raw):
            ip_layer = packet.getlayer(IP)
            tcp_layer = packet.getlayer(TCP)

            # Chỉ xử lý packet đến/từ target IP và port
            if not ((ip_layer.src == self.target_ip or ip_layer.dst == self.target_ip) and
                   (tcp_layer.sport == self.target_port or tcp_layer.dport == self.target_port)):
                return

            raw_data = packet[Raw].load
            data_bytes = bytes(raw_data)

            # In ra console các packet không bắt đầu bằng AA55 hoặc không kết thúc bằng 55AA
            if not data_bytes.startswith(b'\xAA\x55') or not data_bytes.endswith(b'\x55\xAA'):
                print(f"Packet không chuẩn - IP:{ip_layer.src}:{tcp_layer.sport} -> {ip_layer.dst}:{tcp_layer.dport}")
                print(f"  - Data length: {len(data_bytes)} bytes")
                if len(data_bytes) > 0:
                    print(f"  - Bắt đầu: {data_bytes[:2].hex()}")
                    print(f"  - Kết thúc: {data_bytes[-2:].hex()}")
                print("----------")

            self.process_packet_data(data_bytes)

    def process_packet_data(self, data):
        """Xử lý dữ liệu packet tương tự packet_callback"""
        self.buffer += data

        while True:
            # Tìm end marker
            end_index = self.buffer.find(b'\x55\xAA')
            if end_index == -1:
                break

            # Tìm start marker
            start_index = self.buffer.find(b'\xAA\x55')
            if start_index == -1 or start_index > end_index:
                # Nếu không tìm thấy start marker hoặc start marker sau end marker
                # Bỏ qua packet không đủ và tìm start marker tiếp theo
                next_start = self.buffer.find(b'\xAA\x55', end_index + 2)
                if next_start != -1:
                    self.buffer = self.buffer[next_start:]
                    continue
                else:
                    break

            # Trích xuất sub_packet
            sub_packet = self.buffer[start_index:end_index+2]
            self.buffer = self.buffer[end_index+2:]

            # Decrypt data nếu cần
            try:
                decrypted_packet = decrypt_data(sub_packet) if len(sub_packet) >= 16 else sub_packet
                print(f"decrypted {decrypted_packet.hex()}")
            except:
                decrypted_packet = sub_packet

            # Lấy opcode
            try:
                request_type, wordid = manage_packet(decrypted_packet)

                if request_type == 145:
                    print(f"[DETECTED] Packet với opcode 145 - WordID: {wordid}")

                    # Parse và lưu shop data
                    parsed_shop_data = self.parse_shop_packet(decrypted_packet)
                    if parsed_shop_data:
                        self.update_shop_data_file(parsed_shop_data)

            except Exception as e:
                print(f"[ERROR] Lỗi khi xử lý packet: {e}")
                continue

        # Xử lý buffer còn lại
        if len(self.buffer) > 0 and not self.buffer.endswith(b'\x55\xAA'):
            incomplete_start = self.buffer.find(b'\xAA\x55')
            if incomplete_start != -1:
                self.buffer = self.buffer[incomplete_start:]
            else:
                self.buffer = b""
    
    def start_capture(self):
        """Bắt đầu capture packet sử dụng scapy"""
        if not self.running:
            self.running = True
            self.sniff_thread = threading.Thread(target=self.sniff_packets)
            self.sniff_thread.daemon = True
            self.sniff_thread.start()
            print(f"[CAPTURING] Đang capture packet từ {self.target_ip}:{self.target_port}")
            print("Nhấn Ctrl+C để dừng capture...")

    def sniff_packets(self):
        """Sniff packets sử dụng scapy"""
        try:
            # Tạo BPF filter để chỉ capture packet từ/đến target IP và port
            bpf_filter = f"host {self.target_ip} and port {self.target_port}"

            # Sử dụng adapter Ethernet (có thể cần điều chỉnh tùy theo hệ thống)
            adapter = 'Ethernet'

            print(f"[FILTER] Sử dụng filter: {bpf_filter}")
            print(f"[ADAPTER] Sử dụng adapter: {adapter}")

            # Bắt đầu sniff
            sniff(
                filter=bpf_filter,
                iface=adapter,
                prn=self.packet_callback,
                store=0,
                stop_filter=lambda x: not self.running
            )

        except Exception as e:
            print(f"[ERROR] Lỗi khi sniff packet: {e}")
        finally:
            print("[STOPPED] Đã dừng capture packet")

    def stop_capture(self):
        """Dừng capture packet"""
        self.running = False
        print("[STOPPING] Đang dừng capture...")

def main():
    # Cấu hình IP và port để capture
    target_ip = "*************"
    target_port = 15000

    # Tạo và khởi chạy listener
    listener = ShopDataListener(target_ip, target_port)

    try:
        listener.start_capture()

        # Giữ chương trình chạy
        while listener.running:
            import time
            time.sleep(1)

    except KeyboardInterrupt:
        print("\n[STOPPING] Đang dừng capture...")
        listener.stop_capture()

if __name__ == "__main__":
    main()
