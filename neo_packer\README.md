# Neo Packer

Neo Packer là một công cụ hiện đại để bắt và phân tích gói tin mạng, <PERSON><PERSON><PERSON><PERSON> thiết kế đặc biệt cho việc phân tích giao thức game. Ứng dụng được viết bằng Golang với giao diện người dùng hiện đại sử dụng thư viện Fyne.

## Tính năng

- Bắt gói tin mạng từ các giao diện mạng
- Lưu gói tin dưới định dạng PCAP tiêu chuẩn
- Phân tích gói tin theo giao thức game
- Hiển thị dữ liệu dưới dạng hex và ASCII
- Lọc gói tin theo loại
- Hỗ trợ chế độ tối/sáng
- Giao diện người dùng hiện đại và dễ sử dụng

## Yêu cầu hệ thống

- Golang 1.18 trở lên
- Quyền root/admin để bắt gói tin (Linux/macOS)
- <PERSON>h<PERSON> viện libpcap (Linux/macOS) hoặc WinPcap/Npcap (Windows)

## Cài đặt

### Từ mã nguồn

1. Clone repository:
   ```
   git clone https://github.com/yourusername/neo_packer.git
   cd neo_packer
   ```

2. Cài đặt các dependencies:
   ```
   go mod download
   ```

3. Build ứng dụng:
   ```
   go build -o neo_packer ./cmd/neo_packer
   ```

### Từ binary

Tải binary phù hợp với hệ điều hành của bạn từ trang [Releases](https://github.com/yourusername/neo_packer/releases).

## Sử dụng

### Chạy ứng dụng

Trên Linux/macOS, bạn cần quyền root để bắt gói tin:

```
sudo ./neo_packer
```

Hoặc cấp quyền CAP_NET_RAW cho binary:

```
sudo setcap cap_net_raw=eip ./neo_packer
./neo_packer
```

Trên Windows, bạn cần chạy với quyền Administrator.

### Bắt gói tin

1. Chọn giao diện mạng từ dropdown
2. Chọn cấu hình máy chủ
3. Nhấn nút "Start Capture"
4. Gói tin sẽ được hiển thị trong danh sách bên trái
5. Nhấn "Stop Capture" khi hoàn thành

### Phân tích gói tin

1. Chọn một gói tin từ danh sách
2. Xem dữ liệu hex và ASCII ở bảng bên phải
3. Sử dụng bộ lọc để tìm các gói tin theo loại

## Cấu trúc dự án

```
neo_packer/
├── cmd/
│   └── neo_packer/       # Entry point
├── internal/
│   ├── capture/          # Packet capture logic
│   ├── models/           # Data models
│   ├── parser/           # Packet parsing logic
│   └── ui/               # User interface
├── assets/               # Static assets
├── go.mod                # Go modules
└── go.sum                # Dependencies checksums
```

## Đóng góp

Đóng góp luôn được chào đón! Vui lòng xem [CONTRIBUTING.md](CONTRIBUTING.md) để biết thêm chi tiết.

## Giấy phép

Dự án này được phân phối dưới giấy phép MIT. Xem file [LICENSE](LICENSE) để biết thêm chi tiết.
