import struct
import csv
import sqlite3

from prettytable import PrettyTable

def initialize_db():
    conn = sqlite3.connect("npc_data.db")
    cursor = conn.cursor()
    cursor.execute(
        """
        CREATE TABLE IF NOT EXISTS npc_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            wordid INTEGER,
            fld_index1 INTEGER UNIQUE,
            fld_index2 INTEGER,
            fld_pid INTEGER,
            npc_death INTEGER,
            npc_hp INTEGER,
            npc_hp_max INTEGER,
            fld_x REAL,
            fld_z REAL,
            fld_y REAL,
            fld_face1 REAL,
            fld_face2 REAL,
            fld_x2 REAL,
            fld_z2 REAL,
            fld_y2 REAL,
            fld_boss INTEGER,
            boss_flag INTEGER,
            unknown1 INTEGER,
            map_flag INTEGER,
            unknown2 INTEGER,
            max_value INTEGER
        )
    """
    )
    conn.commit()
    conn.close()


initialize_db()


def decode_packet(encrypted_packet):
    try:
        if len(encrypted_packet) < 15:
            raise ValueError("Invalid packet length")
        
        if (
            encrypted_packet[0] != 0xAA
            or encrypted_packet[1] != 0x55
            or encrypted_packet[-2] != 0x55
            or encrypted_packet[-1] != 0xAA
        ):
            raise ValueError("Invalid packet header/footer")
       
        adjusted_data = bytearray(encrypted_packet)
        offset = 6
        wordid = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
        request_type = struct.unpack("<H", adjusted_data[offset + 2 : offset + 4])[0]
        request_length = struct.unpack("<H", adjusted_data[offset + 4 : offset + 6])[0]
        offset += 6
        
        if request_type == 642:
            page = struct.unpack("<I", adjusted_data[offset: offset+4])[0]
            offset+=16
            total = struct.unpack("<I", adjusted_data[offset: offset+4])[0]
            offset+=4
            for _ in range(total):
                offset+=16
                pid = struct.unpack("<I", adjusted_data[offset: offset+4])[0]
                offset+=40
                price =struct.unpack("<I", adjusted_data[offset: offset+4])[0]
                offset+=4
                sale_price = struct.unpack("<I", adjusted_data[offset: offset+4])[0]
                offset+=100
                print(f" id {pid} price {price} sale {sale_price}");

        if request_type == 103:
            npc_count = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
            print(f"npc_count: {npc_count}")
            offset += 4  # 14
            npc_data_list = []

            for _ in range(npc_count):
                fld_index1 = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                fld_index2 = struct.unpack(
                    "<I", adjusted_data[offset + 4 : offset + 8]
                )[0]
                offset += 8  # 18

                fld_pid = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
                offset += 2

                npc_death = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
                offset += 2

                npc_hp = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                npc_hp_max = struct.unpack(
                    "<I", adjusted_data[offset + 4 : offset + 8]
                )[0]
                offset += 8  # 26

                fld_x = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
                fld_z = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[0]
                fld_y = struct.unpack("<f", adjusted_data[offset + 8 : offset + 12])[0]
                offset += 12

                offset += 4

                fld_face1 = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
                fld_face2 = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[
                    0
                ]
                offset += 8

                fld_x2 = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
                fld_z2 = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[0]
                fld_y2 = struct.unpack("<f", adjusted_data[offset + 8 : offset + 12])[0]
                offset += 12

                fld_boss = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                offset += 4

                boss_flag = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                offset += 4

                unknown1 = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                map_flag = struct.unpack("<I", adjusted_data[offset + 4 : offset + 8])[
                    0
                ]
                offset += 8

                unknown2 = struct.unpack("<I", adjusted_data[offset : offset + 4])[0]
                max_value = struct.unpack("<I", adjusted_data[offset + 4 : offset + 8])[
                    0
                ]
                offset += 8

                npc_data = {
                    "fld_index1": fld_index1,
                    "fld_index2": fld_index2,
                    "fld_pid": fld_pid,
                    "npc_death": npc_death,
                    "npc_hp": npc_hp,
                    "npc_hp_max": npc_hp_max,
                    "fld_x": fld_x,
                    "fld_z": fld_z,
                    "fld_y": fld_y,
                    "fld_face1": fld_face1,
                    "fld_face2": fld_face2,
                    "fld_x2": fld_x2,
                    "fld_z2": fld_z2,
                    "fld_y2": fld_y2,
                    "fld_boss": fld_boss,
                    "boss_flag": boss_flag,
                    "unknown1": unknown1,
                    "map_flag": map_flag,
                    "unknown2": unknown2,
                    "max_value": max_value,
                }

                npc_data_list.append(npc_data)

            return {
                "wordid": wordid,
                "request_type": request_type,
                "npc_data_list": npc_data_list,
            }
        if request_type == 209:
            # npcmove
            fld_x = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2
            fld_y = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2
            fld_z = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2
            fld_map = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2
            mapdata = {
                "wordid": wordid,
                "request_type": request_type,
                "fld_x": fld_x,
                "fld_y": fld_y,
                "fld_z": fld_z,
                "fld_map": fld_map,
            }
            return mapdata
        if request_type == 72:
            # Death to city
            print("handle map changer")
            move_type = struct.unpack("<B", adjusted_data[offset : offset + 1])[0]
            print(move_type)
            movedata = {
                "wordid": wordid,
                "request_type": request_type,
                "move_type": move_type,
            }
            match move_type:
                case 88:
                    # Teleporter_move change map
                    numcase_2 = struct.unpack(
                        "<I", adjusted_data[offset + 14 : offset + 18]
                    )[0]
                    movedata["fld_map"] = numcase_2
                    return movedata
                case 99:
                    numcase_2 = struct.unpack(
                        "<H", adjusted_data[offset + 16 : offset + 18]
                    )[0]
                    movedata["fld_map"] = numcase_2
                    return movedata
                case _:
                    return movedata
        if request_type == 121:
            print("Changing Map using item")
            offset = 28
            map_packet = read2(adjusted_data, offset)
            movedata = {
                "wordid": wordid,
                "request_type": request_type,
            }
            if map_packet:
                movedata["fld_map"] = map_packet
                return movedata
            return movedata
        if request_type == 576:
            offset = 15
            hex_to_find = "00007041"
            hex_string = encrypted_packet.hex()
            index = hex_string.find(hex_to_find)
            npc_data_list=[]
            if index == -1:
                return {"wordid": wordid, "request_type": request_type}
            offset2 = 14
            offset = index // 2 - 18 - offset2
            fld_index = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += offset2
            offset += 4
            fld_pid = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_death = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_hp = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 6

            fld_x = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
            fld_z = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[0]
            fld_y = struct.unpack("<f", adjusted_data[offset + 8 : offset + 12])[0]
           
            if fld_pid == 0 or npc_death != 1:
                return {"wordid": wordid, "request_type": request_type}
            print(fld_index, fld_pid, npc_death, npc_hp, fld_x, fld_z, fld_y)
            npc_data_list.append( {
                "fld_index1": fld_index,
                "fld_index2": fld_index,
                "fld_pid": fld_pid,
                "npc_death": npc_death,
                "npc_hp": npc_hp,
                "npc_hp_max": npc_hp,
                "fld_x": fld_x,
                "fld_z": fld_z,
                "fld_y": fld_y,
                "fld_face1": 0,
                "fld_face2": 0,
                "fld_x2": fld_x,
                "fld_z2": fld_z,
                "fld_y2": fld_y,
                "fld_boss": 0,
                "boss_flag": 0,
                "unknown1": 0,
                "map_flag": 0,
                "unknown2": 0,
                "max_value": 0,
            })
            return {
                "wordid": wordid,
                "request_type": request_type,
                "npc_data_list": npc_data_list,
            }
       

        if request_type == 584:
            hex_to_find = "00007041"
            hex_string = encrypted_packet.hex()
            index = hex_string.find(hex_to_find)
            npc_data_list=[]
            if index == -1:
                print("hex not found")
                return {"wordid": wordid, "request_type": request_type}
            offset2 = 1
            offset = index // 2 - 18 - offset2
            fld_index = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += offset2
            offset += 4
            fld_pid = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_death = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 2

            npc_hp = struct.unpack("<H", adjusted_data[offset : offset + 2])[0]
            offset += 6

            fld_x = struct.unpack("<f", adjusted_data[offset : offset + 4])[0]
            fld_z = struct.unpack("<f", adjusted_data[offset + 4 : offset + 8])[0]
            fld_y = struct.unpack("<f", adjusted_data[offset + 8 : offset + 12])[0]
            
            if fld_pid == 0 or npc_death != 1:
                print("invalid data")
                return {"wordid": wordid, "request_type": request_type}
            print(fld_index, fld_pid, npc_death, npc_hp, fld_x, fld_z, fld_y)
            npc_data_list.append( {
                "fld_index1": fld_index,
                "fld_index2": fld_index,
                "fld_pid": fld_pid,
                "npc_death": npc_death,
                "npc_hp": npc_hp,
                "npc_hp_max": npc_hp,
                "fld_x": fld_x,
                "fld_z": fld_z,
                "fld_y": fld_y,
                "fld_face1": 0,
                "fld_face2": 0,
                "fld_x2": fld_x,
                "fld_z2": fld_z,
                "fld_y2": fld_y,
                "fld_boss": 0,
                "boss_flag": 0,
                "unknown1": 0,
                "map_flag": 0,
                "unknown2": 0,
                "max_value": 0,
            })
            return {
                "wordid": wordid,
                "request_type": request_type,
                "npc_data_list": npc_data_list,
            }
        if request_type != 0:
            fld_map_values = unpack_bytes(adjusted_data)
            fld_map_values2 = unpack_bytes1(adjusted_data)
            movedata = {
                "wordid": wordid,
                "request_type": request_type,
                "fld_map": fld_map_values,
                "fld_map2": fld_map_values2,
            }
            return movedata
        return {"wordid": wordid, "request_type": request_type}

    except Exception as e:
        print(f"Error decoding packet: {e}")
        return None


def save_to_csv(npc_data_list, filename="npc_data.csv"):
    with open(filename, mode="w", newline="") as file:
        writer = csv.DictWriter(file, fieldnames=npc_data_list[0].keys())
        writer.writeheader()
        for npc_data in npc_data_list:
            writer.writerow(npc_data)

def get_table_data(wordid_value):
    try:
        conn = sqlite3.connect("npc_data.db")
        cursor = conn.cursor()

        query = """
        SELECT fld_pid, COUNT(fld_pid) as count
        FROM npc_data
        WHERE wordid = ?
        GROUP BY fld_pid
        ORDER BY fld_pid;
        """

        cursor.execute(query, (wordid_value,))
        results = cursor.fetchall()
        
        conn.close()
        
        return results

    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        return []

def display_table(results):
    table = PrettyTable()
    table.field_names = ["fld_pid", "count"]
    total_monsters = 0

    for fld_pid, count in results:
        table.add_row([fld_pid, count])
        total_monsters += count

    table.add_row(["Total Monsters", total_monsters])
    print(table)

def save_shop_data(packet):
    data = decode_packet(packet)


def save_map_data(packet,map):
    data = decode_packet(packet)
    if "npc_data_list" not in data or "request_type" not in data:
        return
    if data and (data["request_type"] == 103 or data["request_type"] == 576 or data["request_type"] == 584):
        conn = sqlite3.connect("npc_data.db")
        cursor = conn.cursor()
        for npc_data in data["npc_data_list"]:
            cursor.execute(
                "SELECT id FROM npc_data WHERE fld_index1=?", (npc_data["fld_index1"],)
            )
            existing_entry = cursor.fetchone()
           
            if not existing_entry:
                print(f"Writing NPC index: {npc_data["fld_index1"]} - npc_id: {npc_data["fld_pid"]} - channel: {map}")
                cursor.execute(
                    """
                                        INSERT INTO npc_data (
                                            wordid, fld_index1, fld_index2, fld_pid, npc_death, npc_hp, npc_hp_max,
                                            fld_x, fld_z, fld_y, fld_face1, fld_face2, fld_x2, fld_z2, fld_y2,
                                            fld_boss, boss_flag, unknown1, map_flag, unknown2, max_value
                                        )
                                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    """,
                    (
                        map,
                        npc_data["fld_index1"],
                        npc_data["fld_index2"],
                        npc_data["fld_pid"],
                        npc_data["npc_death"],
                        npc_data["npc_hp"],
                        npc_data["npc_hp_max"],
                        npc_data["fld_x"],
                        npc_data["fld_z"],
                        npc_data["fld_y"],
                        npc_data["fld_face1"],
                        npc_data["fld_face2"],
                        npc_data["fld_x2"],
                        npc_data["fld_z2"],
                        npc_data["fld_y2"],
                        npc_data["fld_boss"],
                        npc_data["boss_flag"],
                        npc_data["unknown1"],
                        npc_data["map_flag"],
                        npc_data["unknown2"],
                        npc_data["max_value"],
                    ),
                )
            else:
                print(f"NPC Exists - {npc_data["fld_index1"]} -{npc_data["fld_pid"]}")      
        conn.commit()
        conn.close()
