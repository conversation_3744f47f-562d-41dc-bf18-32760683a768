package utils

import (
	"encoding/binary"
	"errors"
	"fmt"
)

func read2(data []byte, offset int) (uint16, error) {
	if offset+2 > len(data) {
		return 0, errors.New("offset out of bounds")
	}
	return binary.LittleEndian.Uint16(data[offset : offset+2]), nil
}

// retrieveOriginalPackage decompresses a packet buffer using a custom compression scheme.
func RetrieveOriginalPackage(packetBuffer []byte, originalLength int) ([]byte, error) {
	fmt.Printf("%x\n", packetBuffer) // Print hex of packetBuffer
	originalPackage := make([]byte, 0, originalLength)
	i := 0

	for i < len(packetBuffer) {
		if packetBuffer[i] < 32 {
			literalLength := int(packetBuffer[i]) + 1
			i++
			for j := 0; j < literalLength && i < len(packetBuffer); j++ {
				originalPackage = append(originalPackage, packetBuffer[i])
				i++
			}
		} else {
			var repeatLength, offset int
			if packetBuffer[i]&0xE0 == 224 {
				if i+2 >= len(packetBuffer) {
					return nil, errors.New("insufficient data for repeat length")
				}
				repeatLength = int(packetBuffer[i]&0x1F) + 7 + 2 + int(packetBuffer[i+1])
				offset = int(packetBuffer[i+2]) + 1
				i += 3
			} else {
				if i+1 >= len(packetBuffer) {
					return nil, errors.New("insufficient data for repeat length")
				}
				repeatLength = int(packetBuffer[i]>>5) + 2
				offset = int(packetBuffer[i+1]) + 1
				i += 2
			}

			for j := 0; j < repeatLength; j++ {
				if len(originalPackage)-offset < 0 {
					return nil, errors.New("invalid offset")
				}
				originalPackage = append(originalPackage, originalPackage[len(originalPackage)-offset])
			}
		}
	}

	if len(originalPackage) < originalLength {
		return nil, errors.New("decompressed data shorter than expected")
	}
	return originalPackage[:originalLength], nil
}

// decompressPacket decompresses a packet and adds specific headers and footers.
func DecompressPacket(packet []byte) ([]byte, error) {
	// Kiểm tra packet header và kích thước cơ bản
	if len(packet) < 16 {
		return packet, fmt.Errorf("packet quá ngắn để giải nén: %d bytes, cần tối thiểu 16 bytes", len(packet))
	}

	// Kiểm tra header AA55
	if packet[0] != 0xAA || packet[1] != 0x55 {
		return packet, fmt.Errorf("header packet không hợp lệ để giải nén: %02X%02X, cần AA55", packet[0], packet[1])
	}

	// Kiểm tra footer 55AA
	if packet[len(packet)-2] != 0x55 || packet[len(packet)-1] != 0xAA {
		return packet, fmt.Errorf("footer packet không hợp lệ để giải nén: %02X%02X, cần 55AA", packet[len(packet)-2], packet[len(packet)-1])
	}

	// Đọc kích thước ban đầu
	originalLength, err := read2(packet, 14)
	if err != nil {
		return packet, fmt.Errorf("lỗi đọc kích thước ban đầu: %v", err)
	}

	// Kiểm tra kích thước ban đầu hợp lý
	if originalLength < 1 || originalLength > 65535 {
		return packet, fmt.Errorf("kích thước ban đầu không hợp lệ: %d", originalLength)
	}

	// Đọc kích thước nén
	compressedLength, err := read2(packet, 12)
	if err != nil {
		return packet, fmt.Errorf("lỗi đọc kích thước nén: %v", err)
	}

	// Kiểm tra kích thước nén hợp lý
	if compressedLength < 1 || compressedLength > 65535 {
		return packet, fmt.Errorf("kích thước nén không hợp lệ: %d", compressedLength)
	}

	// Kiểm tra độ dài packet có đủ cho dữ liệu nén không
	if len(packet) < int(16+compressedLength) {
		return packet, fmt.Errorf("packet không đủ dài cho dữ liệu nén: cần %d bytes, chỉ có %d bytes",
			16+compressedLength, len(packet))
	}

	// Trích xuất dữ liệu nén
	compressedData := packet[16 : 16+compressedLength]

	// Log thông tin giải nén
	fmt.Printf("Dữ liệu nén (độ dài: %d bytes): %x\n", len(compressedData), compressedData[:min(20, len(compressedData))])
	fmt.Printf("Kích thước nén: %d, kích thước gốc: %d\n", compressedLength, originalLength)

	// Giải nén dữ liệu
	decompressedData, err := RetrieveOriginalPackage(compressedData, int(originalLength))
	if err != nil {
		return packet, fmt.Errorf("lỗi giải nén: %v", err)
	}

	// Log thông tin kết quả
	fmt.Printf("Dữ liệu đã giải nén (độ dài: %d bytes): %x\n", len(decompressedData), decompressedData[:min(20, len(decompressedData))])

	// Tạo packet kết quả với header AA55 và footer 55AA
	result := make([]byte, 0, 4+len(decompressedData)+2)
	result = append(result, 0xAA, 0x55, 0x00, 0x00)
	result = append(result, decompressedData...)
	result = append(result, 0x55, 0xAA)

	return result, nil
}

// Hàm phụ trợ để lấy giá trị nhỏ hơn trong hai số
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
