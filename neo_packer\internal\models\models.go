package models

import (
	"time"
)

// Server represents a game server configuration
type Server struct {
	ID          int64
	Name        string
	IP          string
	TargetPort  int
	LoginPort   int
	IsLocal     bool
	IsEncrypted bool
}

// Packet represents a captured network packet
type Packet struct {
	Timestamp   time.Time
	SrcIP       string
	SrcPort     int
	DstIP       string
	DstPort     int
	RequestType int
	Direction   string
	RawData     []byte
	WorldID     int
}

// CaptureSession represents a packet capture session
type CaptureSession struct {
	StartTime     time.Time
	EndTime       time.Time
	ServerInfo    Server
	InterfaceName string
	PacketCount   int
	FilePath      string
}

// PacketType represents a type of packet
type PacketType struct {
	ID   int
	Name string
}

// HexData represents formatted hex data for display
type HexData struct {
	Address string
	HexStr  string
	ByteStr string
}

// DataInterpretation represents different interpretations of binary data
type DataInterpretation struct {
	Hex      string
	Int8     int8
	UInt8    uint8
	Int16    int16
	UInt16   uint16
	Int32    int32
	UInt32   uint32
	Int64    int64
	UInt64   uint64
	Float32  float32
	Float64  float64
	ANSIChar string
	Wide<PERSON>har string
}

// NetworkInterface represents a network interface on the system
type NetworkInterface struct {
	Name        string
	Description string
	IsUp        bool
}

// PortRange represents a range of ports
type PortRange struct {
	Start int
	End   int
}

// CaptureProfile represents a profile for packet capture
type CaptureProfile struct {
	ID          int64
	Name        string
	Description string
	IP          string
	Ports       []int       // Individual ports
	PortRanges  []PortRange // Port ranges
	IsActive    bool
}

// AppConfig represents application configuration
type AppConfig struct {
	LastServer        *Server
	LastInterface     string
	LastSavePath      string
	WindowWidth       int
	WindowHeight      int
	DarkMode          bool
	AutoScrollPackets bool
	CaptureProfiles   []CaptureProfile
}
