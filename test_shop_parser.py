#!/usr/bin/env python3
"""
Test script to verify the updated shop parser functionality
"""
import struct
from shop_listener import Shop<PERSON><PERSON><PERSON><PERSON><PERSON>

def create_test_packet():
    """Create a sample shop packet for testing"""
    # Header: AA55
    packet = b'\xAA\x55'
    
    # Add some padding/header bytes
    packet += b'\x00' * 14  # Padding to reach offset 0x10
    
    # Check value = 3 at offset 0x10
    packet += struct.pack('<I', 3)
    
    # Shop ID = 123 at offset 0x14
    packet += struct.pack('<I', 123)
    
    # Item count = 2 at offset 0x18
    packet += struct.pack('<I', 2)
    
    # 4 bytes empty at offset 0x1C
    packet += b'\x00' * 4
    
    # Tab value = 1 at offset 0x20
    packet += struct.pack('<Q', 1)
    
    # Item 1: FLD_PID = 100200002
    packet += struct.pack('<Q', 100200002)
    # Magic count = 1
    packet += struct.pack('<Q', 1)
    # Magic value 0 = 2000000010
    packet += struct.pack('<Q', 2000000010)
    # End marker = -1
    packet += struct.pack('<q', -1)
    
    # Item 2: FLD_PID = 100200003
    packet += struct.pack('<Q', 100200003)
    # Magic count = 0
    packet += struct.pack('<Q', 0)
    # End marker = -1
    packet += struct.pack('<q', -1)
    
    # Footer: 55AA
    packet += b'\x55\xAA'
    
    return packet

def test_parser():
    """Test the updated parser"""
    print("Testing updated shop parser...")
    
    # Create test packet
    test_packet = create_test_packet()
    print(f"Test packet length: {len(test_packet)} bytes")
    print(f"Test packet hex: {test_packet.hex()}")
    
    # Create listener instance
    listener = ShopDataListener()
    
    # Test parsing
    parsed_data = listener.parse_shop_packet(test_packet)
    
    if parsed_data:
        print("\n✅ Parsing successful!")
        print(f"Shop ID: {parsed_data['shop_id']}")
        print(f"Item count: {parsed_data['item_count']}")
        print(f"Tab value: {parsed_data['tab_value']}")
        
        print("\nItems:")
        for i, item in enumerate(parsed_data['items']):
            print(f"  Item {i}:")
            print(f"    Position: {item['position']}")
            print(f"    FLD_PID: {item['fld_pid']}")
            print(f"    Magic values: {item['magic_values']}")
        
        # Test updating shop data
        print("\nTesting shop data update...")
        listener.update_shop_data_file(parsed_data)
        
        # Load and check the saved data
        shop_data = listener.load_shop_data()
        shop_id = str(123)
        if shop_id in shop_data:
            print("✅ Shop data saved successfully!")
            print(f"Shop {shop_id} has {len(shop_data[shop_id]['items'])} items")
            
            # Check the structure
            for pos, item in shop_data[shop_id]['items'].items():
                print(f"  Position {pos}: PID={item['fld_pid']}, Magic={item['magic_values']}")
        else:
            print("❌ Shop data not found in saved file")
    else:
        print("❌ Parsing failed!")

if __name__ == "__main__":
    test_parser()